#!/bin/bash

# تعيين الترميز
export LANG=en_US.UTF-8
export LC_ALL=en_US.UTF-8

# ألوان للنص
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# دالة لطباعة رسالة ملونة
print_message() {
    echo -e "${2}${1}${NC}"
}

# دالة لطباعة الشعار
print_banner() {
    echo ""
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                                                              ║"
    echo "║        🚀 أداة تحليل العملات الرقمية المتقدمة 🚀           ║"
    echo "║                                                              ║"
    echo "║              Advanced Cryptocurrency Analysis Tool           ║"
    echo "║                                                              ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo ""
}

# دالة للتحقق من وجود أمر
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# طباعة الشعار
print_banner

# التحقق من وجود Python
if ! command_exists python3; then
    if ! command_exists python; then
        print_message "❌ Python غير مثبت على النظام" $RED
        print_message "💡 يرجى تثبيت Python 3.8 أو أحدث" $YELLOW
        echo ""
        echo "Ubuntu/Debian: sudo apt update && sudo apt install python3 python3-pip"
        echo "CentOS/RHEL: sudo yum install python3 python3-pip"
        echo "macOS: brew install python3"
        echo ""
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

print_message "✅ تم العثور على Python" $GREEN
$PYTHON_CMD --version

# التحقق من وجود pip
if ! command_exists pip3 && ! command_exists pip; then
    print_message "❌ pip غير متوفر" $RED
    print_message "💡 يرجى تثبيت pip" $YELLOW
    echo ""
    exit 1
fi

if command_exists pip3; then
    PIP_CMD="pip3"
else
    PIP_CMD="pip"
fi

print_message "✅ تم العثور على pip" $GREEN

# التحقق من وجود ملف المتطلبات
if [ ! -f "requirements.txt" ]; then
    print_message "❌ ملف requirements.txt غير موجود" $RED
    print_message "💡 تأكد من وجود جميع ملفات المشروع" $YELLOW
    echo ""
    exit 1
fi

echo ""
print_message "🔍 فحص المتطلبات..." $BLUE

# محاولة استيراد المكتبات الأساسية
$PYTHON_CMD -c "import pandas, numpy, aiohttp" >/dev/null 2>&1
if [ $? -ne 0 ]; then
    print_message "⚠️  بعض المكتبات المطلوبة غير مثبتة" $YELLOW
    print_message "🔧 جاري تثبيت المتطلبات..." $BLUE
    echo ""
    
    $PIP_CMD install -r requirements.txt
    
    if [ $? -ne 0 ]; then
        print_message "❌ فشل في تثبيت المتطلبات" $RED
        print_message "💡 تحقق من اتصال الإنترنت وحاول مرة أخرى" $YELLOW
        echo ""
        exit 1
    fi
    
    print_message "✅ تم تثبيت المتطلبات بنجاح" $GREEN
else
    print_message "✅ جميع المتطلبات مثبتة" $GREEN
fi

echo ""
print_message "🚀 بدء تشغيل أداة تحليل العملات الرقمية..." $BLUE
echo ""

# تشغيل البرنامج
if [ -f "quick_start.py" ]; then
    $PYTHON_CMD quick_start.py
elif [ -f "run.py" ]; then
    $PYTHON_CMD run.py
elif [ -f "main.py" ]; then
    $PYTHON_CMD main.py
else
    print_message "❌ لم يتم العثور على ملف التشغيل الرئيسي" $RED
    print_message "💡 تأكد من وجود ملف quick_start.py أو run.py أو main.py" $YELLOW
    echo ""
    exit 1
fi

# في حالة انتهاء البرنامج
echo ""
print_message "👋 تم إنهاء البرنامج" $GREEN
print_message "💡 يمكنك إغلاق هذه النافذة الآن" $YELLOW
echo ""

# انتظار إدخال المستخدم قبل الإغلاق
read -p "اضغط Enter للخروج..."