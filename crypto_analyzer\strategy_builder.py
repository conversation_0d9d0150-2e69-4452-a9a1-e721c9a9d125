"""
وحدة بناء استراتيجيات التداول التلقائية
Automated Trading Strategy Builder Module
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import json

class StrategyBuilder:
    """فئة بناء استراتيجيات التداول التلقائية"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # أنواع الاستراتيجيات المتاحة
        self.strategy_types = {
            'trend_following': 'استراتيجية تتبع الاتجاه',
            'mean_reversion': 'استراتيجية العودة للمتوسط',
            'momentum': 'استراتيجية الزخم',
            'breakout': 'استراتيجية الاختراق',
            'scalping': 'استراتيجية السكالبينغ',
            'swing_trading': 'استراتيجية التداول المتأرجح',
            'dca': 'استراتيجية متوسط التكلفة',
            'grid_trading': 'استراتيجية التداول الشبكي'
        }
        
        # معايير تقييم الاستراتيجيات
        self.evaluation_criteria = {
            'profitability': 0.3,      # الربحية
            'risk_adjusted_return': 0.25,  # العائد المعدل للمخاطر
            'win_rate': 0.2,           # معدل النجاح
            'max_drawdown': 0.15,      # أقصى انخفاض
            'volatility': 0.1          # التقلبات
        }
        
        # إعدادات المخاطر
        self.risk_settings = {
            'max_position_size': 0.1,     # أقصى حجم مركز (10%)
            'stop_loss_percent': 0.05,    # وقف الخسارة (5%)
            'take_profit_percent': 0.15,  # جني الأرباح (15%)
            'max_daily_loss': 0.02,       # أقصى خسارة يومية (2%)
            'risk_per_trade': 0.01        # المخاطرة لكل صفقة (1%)
        }
    
    async def build_strategy(self, coin_id: str, analysis_data: Dict) -> Dict:
        """بناء استراتيجية تداول مخصصة للعملة"""
        try:
            # تحليل البيانات المتاحة
            market_data = analysis_data.get('market_data', {})
            technical_analysis = analysis_data.get('technical_analysis', {})
            prediction = analysis_data.get('prediction', {})
            
            # تحديد أفضل استراتيجية
            best_strategy = await self._select_best_strategy(
                coin_id, market_data, technical_analysis, prediction
            )
            
            # بناء قواعد التداول
            trading_rules = self._build_trading_rules(best_strategy, technical_analysis)
            
            # إعداد إدارة المخاطر
            risk_management = self._setup_risk_management(market_data, best_strategy)
            
            # تحديد نقاط الدخول والخروج
            entry_exit_points = self._calculate_entry_exit_points(
                market_data, technical_analysis, best_strategy
            )
            
            # حساب حجم المراكز
            position_sizing = self._calculate_position_sizing(market_data, risk_management)
            
            # تقييم الاستراتيجية
            strategy_evaluation = await self._evaluate_strategy(
                coin_id, best_strategy, trading_rules
            )
            
            return {
                'coin_id': coin_id,
                'strategy_type': best_strategy,
                'strategy_name': self.strategy_types.get(best_strategy, best_strategy),
                'trading_rules': trading_rules,
                'risk_management': risk_management,
                'entry_exit_points': entry_exit_points,
                'position_sizing': position_sizing,
                'evaluation': strategy_evaluation,
                'created_at': datetime.now(),
                'status': 'active',
                'performance_metrics': {}
            }
        
        except Exception as e:
            self.logger.error(f"خطأ في بناء استراتيجية {coin_id}: {e}")
            return {}
    
    async def _select_best_strategy(self, coin_id: str, market_data: Dict,
                                   technical_analysis: Dict, prediction: Dict) -> str:
        """اختيار أفضل استراتيجية للعملة"""
        try:
            strategy_scores = {}
            
            # تحليل خصائص السوق
            market_characteristics = self._analyze_market_characteristics(
                market_data, technical_analysis
            )
            
            # تقييم كل استراتيجية
            for strategy_type in self.strategy_types.keys():
                score = self._score_strategy_for_market(
                    strategy_type, market_characteristics, prediction
                )
                strategy_scores[strategy_type] = score
            
            # اختيار أفضل استراتيجية
            best_strategy = max(strategy_scores, key=strategy_scores.get)
            
            self.logger.info(f"تم اختيار استراتيجية {best_strategy} لـ {coin_id}")
            return best_strategy
        
        except Exception as e:
            self.logger.error(f"خطأ في اختيار الاستراتيجية: {e}")
            return 'trend_following'  # استراتيجية افتراضية
    
    def _analyze_market_characteristics(self, market_data: Dict, 
                                      technical_analysis: Dict) -> Dict:
        """تحليل خصائص السوق"""
        try:
            characteristics = {
                'volatility': 'متوسط',
                'trend_strength': 'متوسط',
                'liquidity': 'متوسط',
                'momentum': 'محايد',
                'market_phase': 'تراكم'
            }
            
            # تحليل التقلبات
            price_change_24h = abs(market_data.get('price_change_percentage_24h', 0))
            if price_change_24h > 10:
                characteristics['volatility'] = 'عالي'
            elif price_change_24h > 5:
                characteristics['volatility'] = 'متوسط'
            else:
                characteristics['volatility'] = 'منخفض'
            
            # تحليل قوة الاتجاه
            trends = technical_analysis.get('trends', {})
            if trends:
                bullish_trends = sum(1 for trend in trends.values() if trend == "صاعد")
                total_trends = len([t for t in trends.values() if t in ["صاعد", "هابط"]])
                
                if total_trends > 0:
                    trend_ratio = bullish_trends / total_trends
                    if trend_ratio >= 0.8:
                        characteristics['trend_strength'] = 'قوي صاعد'
                    elif trend_ratio >= 0.6:
                        characteristics['trend_strength'] = 'متوسط صاعد'
                    elif trend_ratio <= 0.2:
                        characteristics['trend_strength'] = 'قوي هابط'
                    elif trend_ratio <= 0.4:
                        characteristics['trend_strength'] = 'متوسط هابط'
            
            # تحليل السيولة
            volume_24h = market_data.get('volume_24h', 0)
            market_cap = market_data.get('market_cap', 0)
            
            if market_cap > 0:
                volume_ratio = volume_24h / market_cap
                if volume_ratio > 0.1:
                    characteristics['liquidity'] = 'عالي'
                elif volume_ratio > 0.05:
                    characteristics['liquidity'] = 'متوسط'
                else:
                    characteristics['liquidity'] = 'منخفض'
            
            # تحليل الزخم
            indicators = technical_analysis.get('indicators', {})
            if 'rsi' in indicators:
                rsi = indicators['rsi'].get('current', 50)
                if rsi > 60:
                    characteristics['momentum'] = 'صاعد'
                elif rsi < 40:
                    characteristics['momentum'] = 'هابط'
            
            # تحديد مرحلة السوق
            if characteristics['trend_strength'] in ['قوي صاعد', 'متوسط صاعد']:
                if characteristics['momentum'] == 'صاعد':
                    characteristics['market_phase'] = 'نمو'
                else:
                    characteristics['market_phase'] = 'توزيع'
            elif characteristics['trend_strength'] in ['قوي هابط', 'متوسط هابط']:
                if characteristics['momentum'] == 'هابط':
                    characteristics['market_phase'] = 'انخفاض'
                else:
                    characteristics['market_phase'] = 'تراكم'
            
            return characteristics
        
        except Exception as e:
            self.logger.error(f"خطأ في تحليل خصائص السوق: {e}")
            return {}
    
    def _score_strategy_for_market(self, strategy_type: str, 
                                  market_characteristics: Dict, prediction: Dict) -> float:
        """تقييم مناسبة الاستراتيجية للسوق"""
        try:
            score = 0.0
            
            volatility = market_characteristics.get('volatility', 'متوسط')
            trend_strength = market_characteristics.get('trend_strength', 'متوسط')
            liquidity = market_characteristics.get('liquidity', 'متوسط')
            momentum = market_characteristics.get('momentum', 'محايد')
            market_phase = market_characteristics.get('market_phase', 'تراكم')
            
            # تقييم حسب نوع الاستراتيجية
            if strategy_type == 'trend_following':
                # تعمل بشكل أفضل في الأسواق ذات الاتجاه القوي
                if 'قوي' in trend_strength:
                    score += 30
                elif 'متوسط' in trend_strength:
                    score += 20
                
                if market_phase in ['نمو', 'انخفاض']:
                    score += 20
                
                if volatility == 'متوسط':
                    score += 15
            
            elif strategy_type == 'mean_reversion':
                # تعمل بشكل أفضل في الأسواق المتراوحة
                if trend_strength == 'متوسط':
                    score += 25
                
                if volatility == 'عالي':
                    score += 20
                
                if market_phase in ['تراكم', 'توزيع']:
                    score += 20
            
            elif strategy_type == 'momentum':
                # تعمل بشكل أفضل مع الزخم القوي
                if momentum in ['صاعد', 'هابط']:
                    score += 30
                
                if volatility == 'عالي':
                    score += 20
                
                if 'قوي' in trend_strength:
                    score += 15
            
            elif strategy_type == 'breakout':
                # تعمل بشكل أفضل عند الاختراقات
                if volatility == 'عالي':
                    score += 25
                
                if liquidity == 'عالي':
                    score += 20
                
                if market_phase in ['نمو', 'انخفاض']:
                    score += 20
            
            elif strategy_type == 'scalping':
                # تحتاج سيولة عالية وتقلبات منخفضة
                if liquidity == 'عالي':
                    score += 30
                
                if volatility == 'منخفض':
                    score += 20
                
                if trend_strength == 'متوسط':
                    score += 15
            
            elif strategy_type == 'swing_trading':
                # تعمل في معظم ظروف السوق
                score += 20  # نقاط أساسية
                
                if volatility == 'متوسط':
                    score += 15
                
                if liquidity in ['متوسط', 'عالي']:
                    score += 10
            
            elif strategy_type == 'dca':
                # استراتيجية محافظة تعمل في جميع الظروف
                score += 25  # نقاط أساسية
                
                if market_phase == 'تراكم':
                    score += 20
                
                if volatility == 'عالي':
                    score += 10  # فرصة للشراء بأسعار منخفضة
            
            elif strategy_type == 'grid_trading':
                # تعمل بشكل أفضل في الأسواق المتراوحة
                if trend_strength == 'متوسط':
                    score += 25
                
                if volatility == 'متوسط':
                    score += 20
                
                if liquidity == 'عالي':
                    score += 15
            
            # إضافة نقاط من التنبؤات
            if prediction and 'predictions' in prediction:
                predictions = prediction['predictions']
                if 'ensemble' in predictions:
                    ensemble = predictions['ensemble']
                    
                    # تحليل اتجاه التنبؤات
                    positive_predictions = sum(1 for pred in ensemble.values() 
                                             if pred.get('price_change_percent', 0) > 0)
                    total_predictions = len(ensemble)
                    
                    if total_predictions > 0:
                        prediction_ratio = positive_predictions / total_predictions
                        
                        # مكافأة الاستراتيجيات المناسبة للتنبؤات
                        if strategy_type in ['trend_following', 'momentum'] and prediction_ratio > 0.6:
                            score += 15
                        elif strategy_type == 'mean_reversion' and 0.4 <= prediction_ratio <= 0.6:
                            score += 15
            
            return min(100, max(0, score))  # تحديد النتيجة بين 0 و 100
        
        except Exception as e:
            self.logger.error(f"خطأ في تقييم الاستراتيجية {strategy_type}: {e}")
            return 0.0
    
    def _build_trading_rules(self, strategy_type: str, technical_analysis: Dict) -> Dict:
        """بناء قواعد التداول للاستراتيجية"""
        try:
            rules = {
                'entry_conditions': [],
                'exit_conditions': [],
                'stop_loss_rules': [],
                'take_profit_rules': [],
                'position_management': []
            }
            
            indicators = technical_analysis.get('indicators', {})
            trends = technical_analysis.get('trends', {})
            signals = technical_analysis.get('signals', {})
            
            if strategy_type == 'trend_following':
                # قواعد تتبع الاتجاه
                rules['entry_conditions'] = [
                    "السعر أعلى من المتوسط المتحرك 20",
                    "المتوسط المتحرك 5 أعلى من المتوسط المتحرك 20",
                    "MACD أعلى من خط الإشارة",
                    "RSI بين 40 و 80"
                ]
                
                rules['exit_conditions'] = [
                    "السعر أسفل من المتوسط المتحرك 20",
                    "MACD أسفل من خط الإشارة",
                    "RSI أسفل من 30 أو أعلى من 80"
                ]
            
            elif strategy_type == 'mean_reversion':
                # قواعد العودة للمتوسط
                rules['entry_conditions'] = [
                    "السعر أسفل النطاق السفلي لبولينجر",
                    "RSI أقل من 30",
                    "السعر انحرف أكثر من 2% عن المتوسط المتحرك 20"
                ]
                
                rules['exit_conditions'] = [
                    "السعر يصل للمتوسط المتحرك 20",
                    "RSI أعلى من 70",
                    "السعر يصل للنطاق العلوي لبولينجر"
                ]
            
            elif strategy_type == 'momentum':
                # قواعد الزخم
                rules['entry_conditions'] = [
                    "RSI أعلى من 60",
                    "MACD في اتجاه صاعد",
                    "حجم التداول أعلى من المتوسط",
                    "كسر مستوى مقاومة مهم"
                ]
                
                rules['exit_conditions'] = [
                    "RSI أسفل من 40",
                    "MACD يتحول للاتجاه الهابط",
                    "انخفاض حجم التداول"
                ]
            
            elif strategy_type == 'breakout':
                # قواعد الاختراق
                rules['entry_conditions'] = [
                    "كسر مستوى مقاومة بحجم عالي",
                    "السعر أعلى من أعلى سعر في 20 يوم",
                    "ATR في ازدياد",
                    "تأكيد الاختراق بإغلاق أعلى من المقاومة"
                ]
                
                rules['exit_conditions'] = [
                    "فشل في الحفاظ على مستوى الاختراق",
                    "انخفاض حجم التداول بشكل كبير",
                    "ظهور إشارات انعكاس"
                ]
            
            elif strategy_type == 'dca':
                # قواعد متوسط التكلفة
                rules['entry_conditions'] = [
                    "شراء دوري كل أسبوع/شهر",
                    "زيادة الشراء عند انخفاض السعر 10%",
                    "تقليل الشراء عند ارتفاع السعر 20%"
                ]
                
                rules['exit_conditions'] = [
                    "بيع جزئي عند تحقيق ربح 50%",
                    "بيع تدريجي عند الوصول لهدف الاستثمار",
                    "إعادة توازن المحفظة شهرياً"
                ]
            
            # إضافة قواعد إدارة المخاطر العامة
            rules['stop_loss_rules'] = [
                f"وقف الخسارة عند {self.risk_settings['stop_loss_percent']*100}%",
                "وقف الخسارة المتحرك عند تحقيق ربح 10%",
                "إغلاق المركز عند كسر الدعم الرئيسي"
            ]
            
            rules['take_profit_rules'] = [
                f"جني الأرباح عند {self.risk_settings['take_profit_percent']*100}%",
                "بيع جزئي عند مستويات المقاومة",
                "جني أرباح تدريجي عند ظهور إشارات ضعف"
            ]
            
            rules['position_management'] = [
                f"حد أقصى {self.risk_settings['max_position_size']*100}% من المحفظة",
                "تقسيم الدخول على 2-3 مراحل",
                "مراجعة المركز يومياً",
                "إعادة تقييم الاستراتيجية أسبوعياً"
            ]
            
            return rules
        
        except Exception as e:
            self.logger.error(f"خطأ في بناء قواعد التداول: {e}")
            return {}
    
    def _setup_risk_management(self, market_data: Dict, strategy_type: str) -> Dict:
        """إعداد إدارة المخاطر"""
        try:
            risk_management = {
                'max_position_size': self.risk_settings['max_position_size'],
                'stop_loss_percent': self.risk_settings['stop_loss_percent'],
                'take_profit_percent': self.risk_settings['take_profit_percent'],
                'max_daily_loss': self.risk_settings['max_daily_loss'],
                'risk_per_trade': self.risk_settings['risk_per_trade'],
                'position_sizing_method': 'fixed_percent',
                'risk_level': 'متوسط'
            }
            
            # تعديل المخاطر حسب تقلبات العملة
            volatility = abs(market_data.get('price_change_percentage_24h', 0))
            
            if volatility > 15:
                # عملة عالية التقلبات - تقليل المخاطر
                risk_management['max_position_size'] *= 0.5
                risk_management['stop_loss_percent'] *= 0.7
                risk_management['risk_per_trade'] *= 0.5
                risk_management['risk_level'] = 'عالي'
            
            elif volatility < 3:
                # عملة منخفضة التقلبات - يمكن زيادة المخاطر قليلاً
                risk_management['max_position_size'] *= 1.2
                risk_management['take_profit_percent'] *= 0.8
                risk_management['risk_level'] = 'منخفض'
            
            # تعديل حسب نوع الاستراتيجية
            if strategy_type == 'scalping':
                risk_management['stop_loss_percent'] *= 0.5
                risk_management['take_profit_percent'] *= 0.3
                risk_management['max_position_size'] *= 1.5
            
            elif strategy_type == 'swing_trading':
                risk_management['stop_loss_percent'] *= 1.5
                risk_management['take_profit_percent'] *= 2.0
            
            elif strategy_type == 'dca':
                risk_management['stop_loss_percent'] = 0  # لا يوجد وقف خسارة في DCA
                risk_management['position_sizing_method'] = 'dollar_cost_average'
            
            # إضافة قواعد إضافية
            risk_management['rules'] = [
                "عدم تجاوز الحد الأقصى للخسارة اليومية",
                "إغلاق جميع المراكز عند خسارة 5% من المحفظة",
                "مراجعة وتعديل حدود المخاطر شهرياً",
                "عدم فتح مراكز جديدة بعد 3 خسائر متتالية"
            ]
            
            return risk_management
        
        except Exception as e:
            self.logger.error(f"خطأ في إعداد إدارة المخاطر: {e}")
            return {}
    
    def _calculate_entry_exit_points(self, market_data: Dict, 
                                   technical_analysis: Dict, strategy_type: str) -> Dict:
        """حساب نقاط الدخول والخروج"""
        try:
            current_price = market_data.get('current_price', 0)
            
            if current_price == 0:
                return {}
            
            entry_exit = {
                'entry_points': [],
                'exit_points': [],
                'stop_loss_levels': [],
                'take_profit_levels': []
            }
            
            # نقاط الدخول الأساسية
            support_resistance = technical_analysis.get('support_resistance', {})
            
            if support_resistance:
                # نقاط الدخول عند مستويات الدعم
                support_levels = support_resistance.get('support_levels', [])
                for support in support_levels:
                    if support < current_price:
                        entry_exit['entry_points'].append({
                            'price': round(support, 6),
                            'type': 'دعم',
                            'confidence': 'متوسط'
                        })
                
                # نقاط الخروج عند مستويات المقاومة
                resistance_levels = support_resistance.get('resistance_levels', [])
                for resistance in resistance_levels:
                    if resistance > current_price:
                        entry_exit['exit_points'].append({
                            'price': round(resistance, 6),
                            'type': 'مقاومة',
                            'confidence': 'متوسط'
                        })
            
            # نقاط إضافية حسب نوع الاستراتيجية
            if strategy_type == 'trend_following':
                # دخول عند كسر المقاومة
                entry_exit['entry_points'].append({
                    'price': round(current_price * 1.02, 6),
                    'type': 'كسر مقاومة',
                    'confidence': 'عالي'
                })
                
                # خروج عند كسر الدعم
                entry_exit['exit_points'].append({
                    'price': round(current_price * 0.95, 6),
                    'type': 'كسر دعم',
                    'confidence': 'عالي'
                })
            
            elif strategy_type == 'mean_reversion':
                # دخول عند الانحراف عن المتوسط
                entry_exit['entry_points'].extend([
                    {
                        'price': round(current_price * 0.95, 6),
                        'type': 'انحراف سفلي',
                        'confidence': 'عالي'
                    },
                    {
                        'price': round(current_price * 0.90, 6),
                        'type': 'انحراف كبير',
                        'confidence': 'متوسط'
                    }
                ])
                
                # خروج عند العودة للمتوسط
                entry_exit['exit_points'].append({
                    'price': round(current_price * 1.05, 6),
                    'type': 'عودة للمتوسط',
                    'confidence': 'عالي'
                })
            
            # مستويات وقف الخسارة
            stop_loss_percent = self.risk_settings['stop_loss_percent']
            entry_exit['stop_loss_levels'] = [
                {
                    'price': round(current_price * (1 - stop_loss_percent), 6),
                    'type': 'وقف خسارة ثابت',
                    'percent': f"{stop_loss_percent*100}%"
                },
                {
                    'price': round(current_price * (1 - stop_loss_percent*1.5), 6),
                    'type': 'وقف خسارة موسع',
                    'percent': f"{stop_loss_percent*1.5*100}%"
                }
            ]
            
            # مستويات جني الأرباح
            take_profit_percent = self.risk_settings['take_profit_percent']
            entry_exit['take_profit_levels'] = [
                {
                    'price': round(current_price * (1 + take_profit_percent*0.5), 6),
                    'type': 'جني أرباح جزئي',
                    'percent': f"{take_profit_percent*0.5*100}%"
                },
                {
                    'price': round(current_price * (1 + take_profit_percent), 6),
                    'type': 'جني أرباح كامل',
                    'percent': f"{take_profit_percent*100}%"
                },
                {
                    'price': round(current_price * (1 + take_profit_percent*2), 6),
                    'type': 'هدف ممتد',
                    'percent': f"{take_profit_percent*2*100}%"
                }
            ]
            
            return entry_exit
        
        except Exception as e:
            self.logger.error(f"خطأ في حساب نقاط الدخول والخروج: {e}")
            return {}
    
    def _calculate_position_sizing(self, market_data: Dict, risk_management: Dict) -> Dict:
        """حساب حجم المراكز"""
        try:
            current_price = market_data.get('current_price', 0)
            
            if current_price == 0:
                return {}
            
            # افتراض رأس مال 10000 دولار (يمكن تخصيصه)
            portfolio_value = 10000
            
            max_position_size = risk_management.get('max_position_size', 0.1)
            risk_per_trade = risk_management.get('risk_per_trade', 0.01)
            stop_loss_percent = risk_management.get('stop_loss_percent', 0.05)
            
            # حساب حجم المركز بناءً على المخاطرة
            max_position_value = portfolio_value * max_position_size
            risk_amount = portfolio_value * risk_per_trade
            
            # حساب حجم المركز بناءً على وقف الخسارة
            if stop_loss_percent > 0:
                position_value_by_risk = risk_amount / stop_loss_percent
                position_value = min(max_position_value, position_value_by_risk)
            else:
                position_value = max_position_value
            
            # حساب عدد الوحدات
            units = position_value / current_price
            
            position_sizing = {
                'portfolio_value': portfolio_value,
                'max_position_value': max_position_value,
                'recommended_position_value': round(position_value, 2),
                'recommended_units': round(units, 8),
                'position_percentage': round((position_value / portfolio_value) * 100, 2),
                'risk_amount': round(risk_amount, 2),
                'risk_percentage': round(risk_per_trade * 100, 2),
                'sizing_method': risk_management.get('position_sizing_method', 'fixed_percent')
            }
            
            # إضافة خيارات متعددة لحجم المركز
            position_sizing['options'] = {
                'conservative': {
                    'value': round(position_value * 0.5, 2),
                    'units': round(units * 0.5, 8),
                    'risk': 'منخفض'
                },
                'moderate': {
                    'value': round(position_value, 2),
                    'units': round(units, 8),
                    'risk': 'متوسط'
                },
                'aggressive': {
                    'value': round(min(position_value * 1.5, max_position_value), 2),
                    'units': round(min(units * 1.5, max_position_value / current_price), 8),
                    'risk': 'عالي'
                }
            }
            
            return position_sizing
        
        except Exception as e:
            self.logger.error(f"خطأ في حساب حجم المراكز: {e}")
            return {}
    
    async def _evaluate_strategy(self, coin_id: str, strategy_type: str, 
                                trading_rules: Dict) -> Dict:
        """تقييم الاستراتيجية"""
        try:
            evaluation = {
                'expected_performance': 'متوسط',
                'risk_level': 'متوسط',
                'complexity': 'متوسط',
                'time_commitment': 'متوسط',
                'market_suitability': 'جيد',
                'pros': [],
                'cons': [],
                'score': 0
            }
            
            # تقييم حسب نوع الاستراتيجية
            if strategy_type == 'trend_following':
                evaluation.update({
                    'expected_performance': 'جيد',
                    'risk_level': 'متوسط',
                    'complexity': 'منخفض',
                    'time_commitment': 'منخفض',
                    'pros': [
                        'بساطة في التطبيق',
                        'مناسبة للمبتدئين',
                        'تعمل بشكل جيد في الاتجاهات القوية'
                    ],
                    'cons': [
                        'قد تعطي إشارات متأخرة',
                        'لا تعمل بشكل جيد في الأسواق المتراوحة'
                    ]
                })
            
            elif strategy_type == 'scalping':
                evaluation.update({
                    'expected_performance': 'متوسط',
                    'risk_level': 'عالي',
                    'complexity': 'عالي',
                    'time_commitment': 'عالي جداً',
                    'pros': [
                        'أرباح سريعة',
                        'تقليل التعرض للمخاطر طويلة المدى'
                    ],
                    'cons': [
                        'تحتاج مراقبة مستمرة',
                        'رسوم تداول عالية',
                        'ضغط نفسي كبير'
                    ]
                })
            
            elif strategy_type == 'dca':
                evaluation.update({
                    'expected_performance': 'جيد',
                    'risk_level': 'منخفض',
                    'complexity': 'منخفض',
                    'time_commitment': 'منخفض',
                    'pros': [
                        'استراتيجية محافظة',
                        'تقليل تأثير التقلبات',
                        'مناسبة للاستثمار طويل المدى'
                    ],
                    'cons': [
                        'عوائد أبطأ',
                        'قد تفوت فرص الشراء بأسعار منخفضة جداً'
                    ]
                })
            
            # حساب النتيجة الإجمالية
            score = 0
            
            # نقاط الأداء المتوقع
            performance_scores = {
                'ممتاز': 30, 'جيد': 25, 'متوسط': 20, 'ضعيف': 10
            }
            score += performance_scores.get(evaluation['expected_performance'], 20)
            
            # نقاط مستوى المخاطر (معكوسة)
            risk_scores = {
                'منخفض': 25, 'متوسط': 20, 'عالي': 15, 'عالي جداً': 10
            }
            score += risk_scores.get(evaluation['risk_level'], 20)
            
            # نقاط البساطة
            complexity_scores = {
                'منخفض': 20, 'متوسط': 15, 'عالي': 10, 'عالي جداً': 5
            }
            score += complexity_scores.get(evaluation['complexity'], 15)
            
            # نقاط الالتزام الزمني (معكوسة)
            time_scores = {
                'منخفض': 15, 'متوسط': 12, 'عالي': 8, 'عالي جداً': 5
            }
            score += time_scores.get(evaluation['time_commitment'], 12)
            
            # نقاط إضافية للمزايا والعيوب
            score += len(evaluation['pros']) * 2
            score -= len(evaluation['cons']) * 1
            
            evaluation['score'] = min(100, max(0, score))
            
            # تحديد التقييم النهائي
            if evaluation['score'] >= 80:
                evaluation['overall_rating'] = 'ممتاز'
            elif evaluation['score'] >= 65:
                evaluation['overall_rating'] = 'جيد'
            elif evaluation['score'] >= 50:
                evaluation['overall_rating'] = 'متوسط'
            else:
                evaluation['overall_rating'] = 'ضعيف'
            
            return evaluation
        
        except Exception as e:
            self.logger.error(f"خطأ في تقييم الاستراتيجية: {e}")
            return {}
    
    def save_strategy(self, strategy: Dict, filename: str = None):
        """حفظ الاستراتيجية"""
        try:
            if not filename:
                coin_id = strategy.get('coin_id', 'unknown')
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"strategy_{coin_id}_{timestamp}.json"
            
            filepath = f"data/strategies/{filename}"
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(strategy, f, ensure_ascii=False, indent=2, default=str)
            
            self.logger.info(f"تم حفظ الاستراتيجية في {filepath}")
        
        except Exception as e:
            self.logger.error(f"خطأ في حفظ الاستراتيجية: {e}")
    
    def load_strategy(self, filepath: str) -> Dict:
        """تحميل استراتيجية محفوظة"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                strategy = json.load(f)
            
            self.logger.info(f"تم تحميل الاستراتيجية من {filepath}")
            return strategy
        
        except Exception as e:
            self.logger.error(f"خطأ في تحميل الاستراتيجية: {e}")
            return {}