@echo off
chcp 65001 >nul
title محلل العملات الرقمية المبسط

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║        🚀 محلل العملات الرقمية المبسط 🚀                  ║
echo ║                                                              ║
echo ║              Simple Crypto Analyzer v2.0                    ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 فحص النظام...

REM التحقق من Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت!
    echo 💡 يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

echo ✅ Python متوفر

REM تثبيت المتطلبات
echo 🔧 تثبيت المتطلبات...
pip install -r requirements.txt --quiet

if %errorlevel% neq 0 (
    echo ⚠️  تحذير: قد تكون بعض المكتبات غير مثبتة بشكل صحيح
    echo 💡 تأكد من اتصال الإنترنت
)

echo ✅ تم التحقق من المتطلبات
echo.
echo 🚀 بدء تشغيل محلل العملات الرقمية...
echo 🌐 سيتم فتح المتصفح تلقائياً...
echo.
echo ⏹️  لإيقاف البرنامج: اضغط Ctrl+C
echo.

REM تشغيل التطبيق
streamlit run app.py

echo.
echo 👋 تم إنهاء البرنامج
pause