# متطلبات أداة تحليل العملات الرقمية
# Cryptocurrency Analysis Tool Requirements

# مكتبات البيانات والتحليل
pandas>=1.5.0
numpy>=1.21.0
scikit-learn>=1.1.0
talib>=0.4.25

# مكتبات الشبكة والAPI
aiohttp>=3.8.0
requests>=2.28.0
feedparser>=6.0.10

# مكتبات تحليل النصوص والمشاعر
textblob>=0.17.1
beautifulsoup4>=4.11.0

# مكتبات واجهة المستخدم (اختيارية)
flask>=2.2.0
flask-socketio>=5.3.0
# مكتبات الشارتات والرسوم البيانية
plotly>=5.11.0
dash>=2.6.0
dash-bootstrap-components>=1.2.0

# مكتبات التحليل الفني المتقدم
ta>=0.10.0
scipy>=1.9.0

# مكتبات الذكاء الاصطناعي والتنبؤ
tensorflow>=2.10.0
keras>=2.10.0

# مكتبات معالجة اللغات الطبيعية المتقدمة
vaderSentiment>=3.3.0
nltk>=3.7

# مكتبات إضافية للواجهة
jinja2>=3.1.0
colorama>=0.4.0
tqdm>=4.64.0
python-dotenv>=0.19.0
asyncio-throttle>=1.0.0

# مكتبات أخرى
joblib>=1.2.0
python-dateutil>=2.8.0
pytz>=2022.1

# مكتبات التطوير والاختبار (اختيارية)
pytest>=7.0.0
pytest-asyncio>=0.21.0