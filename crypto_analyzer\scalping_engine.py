"""
محرك التداول للمضاربات قصيرة المدى (Scalping Engine)
Short-term Trading Engine for Cryptocurrency Scalping
"""

import asyncio
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import json
from dataclasses import dataclass
import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from data_fetcher import CryptoDataFetcher
from technical_analyzer import TechnicalAnalyzer

@dataclass
class ScalpingSignal:
    """إشارة المضاربة قصيرة المدى"""
    coin_id: str
    signal_type: str  # BUY, SELL, HOLD
    strength: float  # 0-1
    entry_price: float
    stop_loss: float
    take_profit: float
    timeframe: str
    timestamp: datetime
    indicators: Dict
    confidence: float

@dataclass
class ScalpingPosition:
    """مركز المضاربة"""
    coin_id: str
    position_type: str  # LONG, SHORT
    entry_price: float
    current_price: float
    quantity: float
    stop_loss: float
    take_profit: float
    entry_time: datetime
    pnl: float
    pnl_percentage: float
    status: str  # OPEN, CLOSED, STOPPED

class ScalpingEngine:
    """محرك المضاربة قصيرة المدى"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # إعدادات المضاربة
        self.config = {
            'timeframes': ['1m', '5m', '15m'],  # الإطارات الزمنية
            'max_positions': 5,  # أقصى عدد مراكز متزامنة
            'risk_per_trade': 0.02,  # 2% مخاطرة لكل صفقة
            'min_profit_ratio': 1.5,  # نسبة الربح للمخاطرة 1:1.5
            'max_holding_time': 60,  # أقصى وقت احتفاظ بالمركز (دقائق)
            'min_volume': 1000000,  # أقل حجم تداول مطلوب
            'spread_threshold': 0.001,  # حد أقصى للفارق السعري
        }
        
        # المراكز النشطة
        self.active_positions: List[ScalpingPosition] = []
        
        # سجل الإشارات
        self.signal_history: List[ScalpingSignal] = []
        
        # إحصائيات الأداء
        self.performance_stats = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'total_pnl': 0.0,
            'win_rate': 0.0,
            'avg_profit': 0.0,
            'avg_loss': 0.0,
            'max_drawdown': 0.0,
            'sharpe_ratio': 0.0
        }
        
        # العملات المناسبة للمضاربة
        self.scalping_coins = [
            'bitcoin', 'ethereum', 'binancecoin', 'cardano', 'solana',
            'avalanche-2', 'polygon', 'chainlink', 'uniswap'
        ]
    
    async def analyze_scalping_opportunities(self) -> List[ScalpingSignal]:
        """تحليل فرص المضاربة قصيرة المدى"""
        signals = []
        
        try:
            async with CryptoDataFetcher() as fetcher:
                # جلب بيانات السوق
                market_data = await fetcher.get_market_data(self.scalping_coins)
                
                for coin_id, coin_data in market_data.items():
                    try:
                        # فحص شروط المضاربة الأساسية
                        if not self._is_suitable_for_scalping(coin_data):
                            continue
                        
                        # جلب البيانات التاريخية قصيرة المدى
                        historical_data = await fetcher.get_historical_data(
                            coin_id, days=1, interval='5m'
                        )
                        
                        if historical_data.empty:
                            continue
                        
                        # تحليل الإشارات
                        signal = await self._analyze_scalping_signal(
                            coin_id, coin_data, historical_data
                        )
                        
                        if signal and signal.strength > 0.6:
                            signals.append(signal)
                            self.signal_history.append(signal)
                    
                    except Exception as e:
                        self.logger.error(f"خطأ في تحليل {coin_id}: {e}")
                        continue
            
            # ترتيب الإشارات حسب القوة
            signals.sort(key=lambda x: x.strength, reverse=True)
            
            return signals[:5]  # أفضل 5 إشارات
        
        except Exception as e:
            self.logger.error(f"خطأ في تحليل فرص المضاربة: {e}")
            return []
    
    def _is_suitable_for_scalping(self, coin_data: Dict) -> bool:
        """فحص ملاءمة العملة للمضاربة"""
        try:
            # فحص الحجم
            volume = coin_data.get('total_volume', 0)
            if volume < self.config['min_volume']:
                return False
            
            # فحص التقلبات
            price_change_24h = abs(coin_data.get('price_change_percentage_24h', 0))
            if price_change_24h < 2 or price_change_24h > 15:  # تقلبات معتدلة
                return False
            
            # فحص القيمة السوقية (تجنب العملات الصغيرة جداً)
            market_cap = coin_data.get('market_cap', 0)
            if market_cap < 1000000000:  # أقل من مليار دولار
                return False
            
            return True
        
        except Exception:
            return False
    
    async def _analyze_scalping_signal(
        self, 
        coin_id: str, 
        coin_data: Dict, 
        historical_data: pd.DataFrame
    ) -> Optional[ScalpingSignal]:
        """تحليل إشارة المضاربة"""
        try:
            # حساب المؤشرات الفنية قصيرة المدى
            indicators = self._calculate_scalping_indicators(historical_data)
            
            # تحديد نوع الإشارة
            signal_type, strength = self._determine_signal_type(indicators)
            
            if signal_type == 'HOLD' or strength < 0.5:
                return None
            
            current_price = coin_data['current_price']
            
            # حساب مستويات الدخول والخروج
            entry_price = current_price
            stop_loss, take_profit = self._calculate_levels(
                current_price, signal_type, indicators
            )
            
            # حساب مستوى الثقة
            confidence = self._calculate_confidence(indicators, coin_data)
            
            return ScalpingSignal(
                coin_id=coin_id,
                signal_type=signal_type,
                strength=strength,
                entry_price=entry_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                timeframe='5m',
                timestamp=datetime.now(),
                indicators=indicators,
                confidence=confidence
            )
        
        except Exception as e:
            self.logger.error(f"خطأ في تحليل إشارة {coin_id}: {e}")
            return None
    
    def _calculate_scalping_indicators(self, df: pd.DataFrame) -> Dict:
        """حساب المؤشرات الفنية للمضاربة"""
        try:
            indicators = {}
            
            # المتوسطات المتحركة السريعة
            indicators['ema_5'] = df['close'].ewm(span=5).mean().iloc[-1]
            indicators['ema_10'] = df['close'].ewm(span=10).mean().iloc[-1]
            indicators['ema_20'] = df['close'].ewm(span=20).mean().iloc[-1]
            
            # RSI سريع
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=7).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=7).mean()
            rs = gain / loss
            indicators['rsi_7'] = (100 - (100 / (1 + rs))).iloc[-1]
            
            # MACD سريع
            ema_12 = df['close'].ewm(span=12).mean()
            ema_26 = df['close'].ewm(span=26).mean()
            macd_line = ema_12 - ema_26
            signal_line = macd_line.ewm(span=9).mean()
            indicators['macd'] = macd_line.iloc[-1]
            indicators['macd_signal'] = signal_line.iloc[-1]
            indicators['macd_histogram'] = (macd_line - signal_line).iloc[-1]
            
            # Bollinger Bands قصيرة المدى
            bb_period = 10
            bb_std = 1.5
            bb_middle = df['close'].rolling(window=bb_period).mean()
            bb_std_dev = df['close'].rolling(window=bb_period).std()
            indicators['bb_upper'] = (bb_middle + (bb_std_dev * bb_std)).iloc[-1]
            indicators['bb_lower'] = (bb_middle - (bb_std_dev * bb_std)).iloc[-1]
            indicators['bb_middle'] = bb_middle.iloc[-1]
            
            # Stochastic سريع
            low_min = df['low'].rolling(window=7).min()
            high_max = df['high'].rolling(window=7).max()
            k_percent = 100 * ((df['close'] - low_min) / (high_max - low_min))
            indicators['stoch_k'] = k_percent.iloc[-1]
            indicators['stoch_d'] = k_percent.rolling(window=3).mean().iloc[-1]
            
            # Williams %R
            indicators['williams_r'] = -100 * ((high_max - df['close']) / (high_max - low_min)).iloc[-1]
            
            # حجم التداول النسبي
            volume_sma = df['volume'].rolling(window=10).mean()
            indicators['volume_ratio'] = df['volume'].iloc[-1] / volume_sma.iloc[-1]
            
            # ATR للتقلبات
            high_low = df['high'] - df['low']
            high_close = np.abs(df['high'] - df['close'].shift())
            low_close = np.abs(df['low'] - df['close'].shift())
            true_range = np.maximum(high_low, np.maximum(high_close, low_close))
            indicators['atr'] = true_range.rolling(window=7).mean().iloc[-1]
            
            # السعر الحالي
            indicators['current_price'] = df['close'].iloc[-1]
            
            return indicators
        
        except Exception as e:
            self.logger.error(f"خطأ في حساب المؤشرات: {e}")
            return {}
    
    def _determine_signal_type(self, indicators: Dict) -> Tuple[str, float]:
        """تحديد نوع الإشارة وقوتها"""
        try:
            buy_signals = 0
            sell_signals = 0
            total_signals = 0
            
            # إشارات المتوسطات المتحركة
            if indicators['ema_5'] > indicators['ema_10'] > indicators['ema_20']:
                buy_signals += 2
            elif indicators['ema_5'] < indicators['ema_10'] < indicators['ema_20']:
                sell_signals += 2
            total_signals += 2
            
            # إشارات RSI
            rsi = indicators.get('rsi_7', 50)
            if rsi < 30:
                buy_signals += 1
            elif rsi > 70:
                sell_signals += 1
            elif 40 < rsi < 60:
                pass  # منطقة محايدة
            total_signals += 1
            
            # إشارات MACD
            if indicators['macd'] > indicators['macd_signal'] and indicators['macd_histogram'] > 0:
                buy_signals += 1
            elif indicators['macd'] < indicators['macd_signal'] and indicators['macd_histogram'] < 0:
                sell_signals += 1
            total_signals += 1
            
            # إشارات Bollinger Bands
            current_price = indicators['current_price']
            if current_price <= indicators['bb_lower']:
                buy_signals += 1
            elif current_price >= indicators['bb_upper']:
                sell_signals += 1
            total_signals += 1
            
            # إشارات Stochastic
            stoch_k = indicators.get('stoch_k', 50)
            stoch_d = indicators.get('stoch_d', 50)
            if stoch_k < 20 and stoch_d < 20 and stoch_k > stoch_d:
                buy_signals += 1
            elif stoch_k > 80 and stoch_d > 80 and stoch_k < stoch_d:
                sell_signals += 1
            total_signals += 1
            
            # إشارات Williams %R
            williams_r = indicators.get('williams_r', -50)
            if williams_r < -80:
                buy_signals += 1
            elif williams_r > -20:
                sell_signals += 1
            total_signals += 1
            
            # إشارات الحجم
            volume_ratio = indicators.get('volume_ratio', 1)
            if volume_ratio > 1.5:  # حجم عالي يدعم الإشارة
                if buy_signals > sell_signals:
                    buy_signals += 1
                elif sell_signals > buy_signals:
                    sell_signals += 1
            total_signals += 1
            
            # تحديد النتيجة النهائية
            if buy_signals > sell_signals:
                signal_type = 'BUY'
                strength = buy_signals / total_signals
            elif sell_signals > buy_signals:
                signal_type = 'SELL'
                strength = sell_signals / total_signals
            else:
                signal_type = 'HOLD'
                strength = 0.0
            
            return signal_type, min(strength, 1.0)
        
        except Exception as e:
            self.logger.error(f"خطأ في تحديد نوع الإشارة: {e}")
            return 'HOLD', 0.0
    
    def _calculate_levels(
        self, 
        current_price: float, 
        signal_type: str, 
        indicators: Dict
    ) -> Tuple[float, float]:
        """حساب مستويات وقف الخسارة وجني الأرباح"""
        try:
            atr = indicators.get('atr', current_price * 0.01)
            
            if signal_type == 'BUY':
                # وقف الخسارة تحت أقل نقطة حديثة
                stop_loss = current_price - (atr * 1.5)
                # جني الأرباح بنسبة 1:1.5
                take_profit = current_price + (atr * 2.25)
                
                # التأكد من مستويات Bollinger Bands
                bb_lower = indicators.get('bb_lower', current_price * 0.99)
                stop_loss = min(stop_loss, bb_lower * 0.998)
                
            else:  # SELL
                # وقف الخسارة فوق أعلى نقطة حديثة
                stop_loss = current_price + (atr * 1.5)
                # جني الأرباح بنسبة 1:1.5
                take_profit = current_price - (atr * 2.25)
                
                # التأكد من مستويات Bollinger Bands
                bb_upper = indicators.get('bb_upper', current_price * 1.01)
                stop_loss = max(stop_loss, bb_upper * 1.002)
            
            return stop_loss, take_profit
        
        except Exception as e:
            self.logger.error(f"خطأ في حساب المستويات: {e}")
            # مستويات افتراضية
            if signal_type == 'BUY':
                return current_price * 0.99, current_price * 1.015
            else:
                return current_price * 1.01, current_price * 0.985
    
    def _calculate_confidence(self, indicators: Dict, coin_data: Dict) -> float:
        """حساب مستوى الثقة في الإشارة"""
        try:
            confidence_factors = []
            
            # عامل الحجم
            volume_ratio = indicators.get('volume_ratio', 1)
            if volume_ratio > 1.5:
                confidence_factors.append(0.2)
            elif volume_ratio > 1.2:
                confidence_factors.append(0.1)
            
            # عامل التقلبات
            atr = indicators.get('atr', 0)
            current_price = indicators.get('current_price', 1)
            volatility_ratio = atr / current_price
            if 0.005 < volatility_ratio < 0.02:  # تقلبات مثالية للمضاربة
                confidence_factors.append(0.15)
            
            # عامل اتجاه السوق العام
            price_change_24h = coin_data.get('price_change_percentage_24h', 0)
            if abs(price_change_24h) < 10:  # تجنب التقلبات الشديدة
                confidence_factors.append(0.1)
            
            # عامل RSI
            rsi = indicators.get('rsi_7', 50)
            if rsi < 30 or rsi > 70:  # مناطق ذروة البيع/الشراء
                confidence_factors.append(0.15)
            
            # عامل MACD
            macd_histogram = indicators.get('macd_histogram', 0)
            if abs(macd_histogram) > 0:
                confidence_factors.append(0.1)
            
            # عامل Bollinger Bands
            current_price = indicators.get('current_price', 0)
            bb_upper = indicators.get('bb_upper', current_price)
            bb_lower = indicators.get('bb_lower', current_price)
            bb_position = (current_price - bb_lower) / (bb_upper - bb_lower)
            if bb_position < 0.2 or bb_position > 0.8:
                confidence_factors.append(0.15)
            
            # عامل القيمة السوقية
            market_cap = coin_data.get('market_cap', 0)
            if market_cap > 10000000000:  # أكثر من 10 مليار
                confidence_factors.append(0.1)
            
            base_confidence = 0.5
            total_confidence = base_confidence + sum(confidence_factors)
            
            return min(total_confidence, 1.0)
        
        except Exception as e:
            self.logger.error(f"خطأ في حساب الثقة: {e}")
            return 0.5
    
    async def execute_scalping_strategy(self) -> Dict:
        """تنفيذ استراتيجية المضاربة"""
        try:
            # تحليل الفرص
            signals = await self.analyze_scalping_opportunities()
            
            results = {
                'timestamp': datetime.now(),
                'signals_found': len(signals),
                'signals': [],
                'recommendations': [],
                'risk_assessment': {}
            }
            
            for signal in signals:
                signal_dict = {
                    'coin_id': signal.coin_id,
                    'signal_type': signal.signal_type,
                    'strength': signal.strength,
                    'confidence': signal.confidence,
                    'entry_price': signal.entry_price,
                    'stop_loss': signal.stop_loss,
                    'take_profit': signal.take_profit,
                    'risk_reward_ratio': self._calculate_risk_reward(signal),
                    'timeframe': signal.timeframe
                }
                
                results['signals'].append(signal_dict)
                
                # توليد التوصيات
                recommendation = self._generate_scalping_recommendation(signal)
                results['recommendations'].append(recommendation)
            
            # تقييم المخاطر العام
            results['risk_assessment'] = self._assess_scalping_risks(signals)
            
            return results
        
        except Exception as e:
            self.logger.error(f"خطأ في تنفيذ استراتيجية المضاربة: {e}")
            return {}
    
    def _calculate_risk_reward(self, signal: ScalpingSignal) -> float:
        """حساب نسبة المخاطرة للعائد"""
        try:
            if signal.signal_type == 'BUY':
                risk = signal.entry_price - signal.stop_loss
                reward = signal.take_profit - signal.entry_price
            else:  # SELL
                risk = signal.stop_loss - signal.entry_price
                reward = signal.entry_price - signal.take_profit
            
            if risk > 0:
                return reward / risk
            else:
                return 0.0
        
        except Exception:
            return 0.0
    
    def _generate_scalping_recommendation(self, signal: ScalpingSignal) -> Dict:
        """توليد توصية المضاربة"""
        try:
            risk_reward = self._calculate_risk_reward(signal)
            
            # تحديد حجم المركز المقترح
            if signal.confidence > 0.8 and risk_reward > 2.0:
                position_size = "كبير (3-5%)"
                urgency = "عالية"
            elif signal.confidence > 0.7 and risk_reward > 1.5:
                position_size = "متوسط (2-3%)"
                urgency = "متوسطة"
            else:
                position_size = "صغير (1-2%)"
                urgency = "منخفضة"
            
            return {
                'coin_id': signal.coin_id,
                'action': signal.signal_type,
                'entry_price': signal.entry_price,
                'stop_loss': signal.stop_loss,
                'take_profit': signal.take_profit,
                'position_size': position_size,
                'urgency': urgency,
                'risk_reward_ratio': risk_reward,
                'confidence': signal.confidence,
                'timeframe': signal.timeframe,
                'notes': self._get_signal_notes(signal)
            }
        
        except Exception as e:
            self.logger.error(f"خطأ في توليد التوصية: {e}")
            return {}
    
    def _get_signal_notes(self, signal: ScalpingSignal) -> List[str]:
        """الحصول على ملاحظات الإشارة"""
        notes = []
        
        try:
            indicators = signal.indicators
            
            # ملاحظات RSI
            rsi = indicators.get('rsi_7', 50)
            if rsi < 30:
                notes.append("RSI في منطقة ذروة البيع")
            elif rsi > 70:
                notes.append("RSI في منطقة ذروة الشراء")
            
            # ملاحظات الحجم
            volume_ratio = indicators.get('volume_ratio', 1)
            if volume_ratio > 1.5:
                notes.append("حجم تداول عالي يدعم الإشارة")
            
            # ملاحظات Bollinger Bands
            current_price = indicators.get('current_price', 0)
            bb_upper = indicators.get('bb_upper', current_price)
            bb_lower = indicators.get('bb_lower', current_price)
            
            if current_price <= bb_lower:
                notes.append("السعر عند الحد السفلي لـ Bollinger Bands")
            elif current_price >= bb_upper:
                notes.append("السعر عند الحد العلوي لـ Bollinger Bands")
            
            # ملاحظات MACD
            if indicators.get('macd_histogram', 0) > 0:
                notes.append("MACD يظهر زخم صاعد")
            elif indicators.get('macd_histogram', 0) < 0:
                notes.append("MACD يظهر زخم هابط")
            
            if not notes:
                notes.append("إشارة فنية عادية")
            
            return notes
        
        except Exception:
            return ["تحليل فني أساسي"]
    
    def _assess_scalping_risks(self, signals: List[ScalpingSignal]) -> Dict:
        """تقييم مخاطر المضاربة"""
        try:
            if not signals:
                return {
                    'overall_risk': 'منخفض',
                    'market_conditions': 'غير مناسبة للمضاربة',
                    'recommendations': ['انتظار فرص أفضل']
                }
            
            # حساب متوسط الثقة
            avg_confidence = sum(s.confidence for s in signals) / len(signals)
            
            # حساب متوسط نسبة المخاطرة للعائد
            avg_risk_reward = sum(self._calculate_risk_reward(s) for s in signals) / len(signals)
            
            # تحديد مستوى المخاطر
            if avg_confidence > 0.8 and avg_risk_reward > 2.0:
                risk_level = 'منخفض'
                market_condition = 'ممتازة للمضاربة'
            elif avg_confidence > 0.7 and avg_risk_reward > 1.5:
                risk_level = 'متوسط'
                market_condition = 'جيدة للمضاربة'
            elif avg_confidence > 0.6:
                risk_level = 'متوسط إلى عالي'
                market_condition = 'مقبولة للمضاربة'
            else:
                risk_level = 'عالي'
                market_condition = 'غير مناسبة للمضاربة'
            
            recommendations = []
            if risk_level == 'منخفض':
                recommendations.extend([
                    'يمكن زيادة حجم المراكز',
                    'مراقبة دقيقة للإشارات',
                    'تنفيذ سريع للصفقات'
                ])
            elif risk_level == 'متوسط':
                recommendations.extend([
                    'حجم مراكز معتدل',
                    'وضع أوامر وقف خسارة محكمة',
                    'مراقبة مستمرة للسوق'
                ])
            else:
                recommendations.extend([
                    'تجنب المضاربة في الوقت الحالي',
                    'انتظار ظروف سوق أفضل',
                    'التركيز على التحليل فقط'
                ])
            
            return {
                'overall_risk': risk_level,
                'market_conditions': market_condition,
                'avg_confidence': avg_confidence,
                'avg_risk_reward': avg_risk_reward,
                'signals_count': len(signals),
                'recommendations': recommendations
            }
        
        except Exception as e:
            self.logger.error(f"خطأ في تقييم المخاطر: {e}")
            return {
                'overall_risk': 'غير محدد',
                'market_conditions': 'غير واضحة',
                'recommendations': ['توخي الحذر الشديد']
            }

# مثال على الاستخدام
async def main():
    """مثال على استخدام محرك المضاربة"""
    engine = ScalpingEngine()
    
    print("🚀 بدء تحليل فرص المضاربة قصيرة المدى...")
    
    results = await engine.execute_scalping_strategy()
    
    print(f"\n📊 تم العثور على {results.get('signals_found', 0)} إشارة")
    
    for recommendation in results.get('recommendations', []):
        print(f"\n💡 توصية {recommendation['coin_id']}:")
        print(f"   العمل: {recommendation['action']}")
        print(f"   سعر الدخول: ${recommendation['entry_price']:.6f}")
        print(f"   وقف الخسارة: ${recommendation['stop_loss']:.6f}")
        print(f"   جني الأرباح: ${recommendation['take_profit']:.6f}")
        print(f"   نسبة المخاطرة/العائد: 1:{recommendation['risk_reward_ratio']:.2f}")
        print(f"   مستوى الثقة: {recommendation['confidence']:.2f}")

if __name__ == "__main__":
    asyncio.run(main())