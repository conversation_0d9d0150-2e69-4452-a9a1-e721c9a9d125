"""
محرك التنبؤ بأسعار العملات الرقمية
Cryptocurrency Price Prediction Engine
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.metrics import mean_absolute_error, mean_squared_error
from sklearn.model_selection import train_test_split
import joblib
import logging
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class PredictionEngine:
    """محرك التنبؤ بأسعار العملات الرقمية"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # النماذج المختلفة للتنبؤ
        self.models = {
            'random_forest': RandomForestRegressor(n_estimators=100, random_state=42),
            'gradient_boosting': GradientBoostingRegressor(n_estimators=100, random_state=42),
            'linear_regression': LinearRegression()
        }
        
        # معالجات البيانات
        self.scalers = {
            'standard': StandardScaler(),
            'minmax': MinMaxScaler()
        }
        
        # إعدادات التنبؤ
        self.prediction_config = {
            'lookback_days': 30,  # عدد الأيام للنظر إلى الوراء
            'prediction_days': [1, 3, 7, 14, 30],  # فترات التنبؤ
            'features': [
                'price', 'volume', 'market_cap', 'price_change_24h',
                'sma_5', 'sma_20', 'ema_12', 'rsi', 'macd'
            ]
        }
        
        # تخزين النماذج المدربة
        self.trained_models = {}
        self.model_performance = {}
    
    async def predict_price(self, coin_id: str, market_data: Dict) -> Dict:
        """التنبؤ بسعر العملة"""
        try:
            # الحصول على البيانات التاريخية
            from data_fetcher import CryptoDataFetcher
            
            async with CryptoDataFetcher() as fetcher:
                historical_df = await fetcher.get_historical_data(coin_id, days=90)
                ohlcv_df = await fetcher.get_ohlcv_data(coin_id, days=90)
            
            if historical_df.empty or ohlcv_df.empty:
                self.logger.warning(f"لا توجد بيانات كافية للتنبؤ بـ {coin_id}")
                return {}
            
            # إعداد البيانات للتنبؤ
            prepared_data = self._prepare_prediction_data(historical_df, ohlcv_df, market_data)
            
            if prepared_data.empty:
                return {}
            
            # تدريب النماذج والتنبؤ
            predictions = await self._generate_predictions(coin_id, prepared_data)
            
            # تحليل الثقة في التنبؤات
            confidence_analysis = self._analyze_prediction_confidence(predictions, prepared_data)
            
            # توليد التوصيات
            recommendations = self._generate_price_recommendations(predictions, market_data)
            
            return {
                'coin_id': coin_id,
                'current_price': market_data.get('current_price', 0),
                'predictions': predictions,
                'confidence': confidence_analysis,
                'recommendations': recommendations,
                'prediction_time': datetime.now(),
                'data_points_used': len(prepared_data)
            }
        
        except Exception as e:
            self.logger.error(f"خطأ في التنبؤ بسعر {coin_id}: {e}")
            return {}
    
    def _prepare_prediction_data(self, historical_df: pd.DataFrame, 
                                ohlcv_df: pd.DataFrame, market_data: Dict) -> pd.DataFrame:
        """إعداد البيانات للتنبؤ"""
        try:
            # دمج البيانات
            df = pd.merge(historical_df, ohlcv_df, left_index=True, right_index=True, how='inner')
            
            if df.empty:
                return pd.DataFrame()
            
            # إضافة المؤشرات الفنية
            df = self._add_technical_indicators(df)
            
            # إضافة ميزات إضافية
            df = self._add_additional_features(df, market_data)
            
            # تنظيف البيانات
            df = df.dropna()
            
            # التأكد من وجود بيانات كافية
            if len(df) < self.prediction_config['lookback_days']:
                self.logger.warning("البيانات غير كافية للتنبؤ")
                return pd.DataFrame()
            
            return df
        
        except Exception as e:
            self.logger.error(f"خطأ في إعداد بيانات التنبؤ: {e}")
            return pd.DataFrame()
    
    def _add_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """إضافة المؤشرات الفنية"""
        try:
            # التأكد من وجود الأعمدة المطلوبة
            if 'close' not in df.columns:
                df['close'] = df['price'] if 'price' in df.columns else df.iloc[:, 0]
            
            # المتوسطات المتحركة
            df['sma_5'] = df['close'].rolling(window=5).mean()
            df['sma_10'] = df['close'].rolling(window=10).mean()
            df['sma_20'] = df['close'].rolling(window=20).mean()
            df['sma_50'] = df['close'].rolling(window=50).mean()
            
            # المتوسطات المتحركة الأسية
            df['ema_12'] = df['close'].ewm(span=12).mean()
            df['ema_26'] = df['close'].ewm(span=26).mean()
            
            # RSI
            df['rsi'] = self._calculate_rsi(df['close'])
            
            # MACD
            df['macd'] = df['ema_12'] - df['ema_26']
            df['macd_signal'] = df['macd'].ewm(span=9).mean()
            df['macd_histogram'] = df['macd'] - df['macd_signal']
            
            # Bollinger Bands
            bb_period = 20
            bb_std = 2
            df['bb_middle'] = df['close'].rolling(window=bb_period).mean()
            bb_std_dev = df['close'].rolling(window=bb_period).std()
            df['bb_upper'] = df['bb_middle'] + (bb_std_dev * bb_std)
            df['bb_lower'] = df['bb_middle'] - (bb_std_dev * bb_std)
            df['bb_width'] = df['bb_upper'] - df['bb_lower']
            df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
            
            # التقلبات
            df['volatility'] = df['close'].rolling(window=20).std()
            df['price_change'] = df['close'].pct_change()
            df['price_change_abs'] = df['price_change'].abs()
            
            return df
        
        except Exception as e:
            self.logger.error(f"خطأ في إضافة المؤشرات الفنية: {e}")
            return df
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """حساب مؤشر RSI"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        except:
            return pd.Series(index=prices.index, dtype=float)
    
    def _add_additional_features(self, df: pd.DataFrame, market_data: Dict) -> pd.DataFrame:
        """إضافة ميزات إضافية"""
        try:
            # ميزات زمنية
            df['hour'] = df.index.hour
            df['day_of_week'] = df.index.dayofweek
            df['day_of_month'] = df.index.day
            df['month'] = df.index.month
            
            # ميزات السوق
            if 'market_cap_rank' in market_data:
                df['market_cap_rank'] = market_data['market_cap_rank']
            
            # نسب مالية
            if 'volume' in df.columns and 'market_cap' in df.columns:
                df['volume_to_market_cap'] = df['volume'] / df['market_cap']
            
            # اتجاهات الأسعار
            df['price_trend_3d'] = df['close'].rolling(window=3).apply(
                lambda x: 1 if x.iloc[-1] > x.iloc[0] else 0
            )
            df['price_trend_7d'] = df['close'].rolling(window=7).apply(
                lambda x: 1 if x.iloc[-1] > x.iloc[0] else 0
            )
            
            # قوة الاتجاه
            df['trend_strength'] = abs(df['close'].rolling(window=10).apply(
                lambda x: (x.iloc[-1] - x.iloc[0]) / x.iloc[0]
            ))
            
            return df
        
        except Exception as e:
            self.logger.error(f"خطأ في إضافة الميزات الإضافية: {e}")
            return df
    
    async def _generate_predictions(self, coin_id: str, df: pd.DataFrame) -> Dict:
        """توليد التنبؤات باستخدام نماذج متعددة"""
        try:
            predictions = {}
            
            # إعداد البيانات للتدريب
            feature_columns = [col for col in df.columns if col not in ['close', 'price']]
            X = df[feature_columns].fillna(0)
            y = df['close']
            
            # تقسيم البيانات
            train_size = int(len(X) * 0.8)
            X_train, X_test = X[:train_size], X[train_size:]
            y_train, y_test = y[:train_size], y[train_size:]
            
            # تطبيق التطبيع
            scaler = self.scalers['standard']
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)
            
            # تدريب النماذج
            model_predictions = {}
            model_scores = {}
            
            for model_name, model in self.models.items():
                try:
                    # تدريب النموذج
                    model.fit(X_train_scaled, y_train)
                    
                    # التنبؤ على بيانات الاختبار
                    test_predictions = model.predict(X_test_scaled)
                    
                    # تقييم الأداء
                    mae = mean_absolute_error(y_test, test_predictions)
                    mse = mean_squared_error(y_test, test_predictions)
                    
                    model_scores[model_name] = {
                        'mae': mae,
                        'mse': mse,
                        'rmse': np.sqrt(mse)
                    }
                    
                    # التنبؤ للفترات المختلفة
                    future_predictions = self._predict_future_prices(
                        model, scaler, X, y, feature_columns
                    )
                    
                    model_predictions[model_name] = future_predictions
                    
                    # حفظ النموذج
                    self.trained_models[f"{coin_id}_{model_name}"] = {
                        'model': model,
                        'scaler': scaler,
                        'features': feature_columns
                    }
                
                except Exception as e:
                    self.logger.warning(f"خطأ في تدريب نموذج {model_name}: {e}")
                    continue
            
            # دمج التنبؤات من النماذج المختلفة
            ensemble_predictions = self._create_ensemble_predictions(model_predictions, model_scores)
            
            predictions = {
                'individual_models': model_predictions,
                'ensemble': ensemble_predictions,
                'model_performance': model_scores,
                'current_price': y.iloc[-1]
            }
            
            return predictions
        
        except Exception as e:
            self.logger.error(f"خطأ في توليد التنبؤات: {e}")
            return {}
    
    def _predict_future_prices(self, model, scaler, X: pd.DataFrame, 
                              y: pd.Series, feature_columns: List[str]) -> Dict:
        """التنبؤ بالأسعار المستقبلية"""
        try:
            future_predictions = {}
            current_features = X.iloc[-1:][feature_columns].fillna(0)
            
            for days in self.prediction_config['prediction_days']:
                try:
                    # استخدام آخر البيانات للتنبؤ
                    features_scaled = scaler.transform(current_features)
                    predicted_price = model.predict(features_scaled)[0]
                    
                    # حساب التغيير المئوي
                    current_price = y.iloc[-1]
                    price_change_percent = ((predicted_price - current_price) / current_price) * 100
                    
                    future_predictions[f'{days}_day'] = {
                        'predicted_price': round(predicted_price, 6),
                        'price_change_percent': round(price_change_percent, 2),
                        'direction': 'صاعد' if price_change_percent > 0 else 'هابط',
                        'target_date': (datetime.now() + timedelta(days=days)).strftime('%Y-%m-%d')
                    }
                
                except Exception as e:
                    self.logger.warning(f"خطأ في التنبؤ لـ {days} يوم: {e}")
                    continue
            
            return future_predictions
        
        except Exception as e:
            self.logger.error(f"خطأ في التنبؤ بالأسعار المستقبلية: {e}")
            return {}
    
    def _create_ensemble_predictions(self, model_predictions: Dict, model_scores: Dict) -> Dict:
        """إنشاء تنبؤات مجمعة من النماذج المختلفة"""
        try:
            if not model_predictions:
                return {}
            
            ensemble_predictions = {}
            
            # حساب الأوزان بناءً على أداء النماذج
            weights = {}
            total_inverse_error = 0
            
            for model_name, scores in model_scores.items():
                # استخدام معكوس RMSE كوزن
                weight = 1 / (scores['rmse'] + 1e-6)
                weights[model_name] = weight
                total_inverse_error += weight
            
            # تطبيع الأوزان
            for model_name in weights:
                weights[model_name] /= total_inverse_error
            
            # دمج التنبؤات
            for days in self.prediction_config['prediction_days']:
                day_key = f'{days}_day'
                weighted_price = 0
                weighted_change = 0
                valid_predictions = 0
                
                for model_name, predictions in model_predictions.items():
                    if day_key in predictions:
                        weight = weights.get(model_name, 0)
                        weighted_price += predictions[day_key]['predicted_price'] * weight
                        weighted_change += predictions[day_key]['price_change_percent'] * weight
                        valid_predictions += 1
                
                if valid_predictions > 0:
                    ensemble_predictions[day_key] = {
                        'predicted_price': round(weighted_price, 6),
                        'price_change_percent': round(weighted_change, 2),
                        'direction': 'صاعد' if weighted_change > 0 else 'هابط',
                        'target_date': (datetime.now() + timedelta(days=days)).strftime('%Y-%m-%d'),
                        'models_used': valid_predictions,
                        'confidence': self._calculate_ensemble_confidence(
                            model_predictions, day_key, weights
                        )
                    }
            
            return ensemble_predictions
        
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء التنبؤات المجمعة: {e}")
            return {}
    
    def _calculate_ensemble_confidence(self, model_predictions: Dict, 
                                     day_key: str, weights: Dict) -> float:
        """حساب مستوى الثقة في التنبؤ المجمع"""
        try:
            predictions = []
            total_weight = 0
            
            for model_name, model_pred in model_predictions.items():
                if day_key in model_pred:
                    weight = weights.get(model_name, 0)
                    predictions.append(model_pred[day_key]['predicted_price'])
                    total_weight += weight
            
            if len(predictions) < 2:
                return 0.5
            
            # حساب التباين في التنبؤات
            predictions_array = np.array(predictions)
            std_dev = np.std(predictions_array)
            mean_price = np.mean(predictions_array)
            
            # تحويل التباين إلى مستوى ثقة (كلما قل التباين، زادت الثقة)
            coefficient_of_variation = std_dev / mean_price if mean_price > 0 else 1
            confidence = max(0, min(1, 1 - coefficient_of_variation))
            
            return round(confidence, 3)
        
        except Exception as e:
            self.logger.error(f"خطأ في حساب مستوى الثقة: {e}")
            return 0.5
    
    def _analyze_prediction_confidence(self, predictions: Dict, df: pd.DataFrame) -> Dict:
        """تحليل مستوى الثقة في التنبؤات"""
        try:
            confidence_analysis = {
                'overall_confidence': 'متوسط',
                'data_quality': 'جيد',
                'model_agreement': 'متوسط',
                'factors': []
            }
            
            # تحليل جودة البيانات
            data_completeness = (len(df) - df.isnull().sum().sum()) / (len(df) * len(df.columns))
            
            if data_completeness >= 0.95:
                confidence_analysis['data_quality'] = 'ممتاز'
                confidence_analysis['factors'].append('جودة البيانات عالية')
            elif data_completeness >= 0.85:
                confidence_analysis['data_quality'] = 'جيد'
            else:
                confidence_analysis['data_quality'] = 'ضعيف'
                confidence_analysis['factors'].append('نقص في البيانات')
            
            # تحليل اتفاق النماذج
            if 'ensemble' in predictions:
                ensemble_preds = predictions['ensemble']
                avg_confidence = np.mean([
                    pred.get('confidence', 0) for pred in ensemble_preds.values()
                ])
                
                if avg_confidence >= 0.8:
                    confidence_analysis['model_agreement'] = 'عالي'
                    confidence_analysis['factors'].append('اتفاق قوي بين النماذج')
                elif avg_confidence >= 0.6:
                    confidence_analysis['model_agreement'] = 'متوسط'
                else:
                    confidence_analysis['model_agreement'] = 'ضعيف'
                    confidence_analysis['factors'].append('تضارب في تنبؤات النماذج')
            
            # تحليل التقلبات
            if 'close' in df.columns:
                volatility = df['close'].pct_change().std()
                if volatility > 0.05:  # تقلبات عالية
                    confidence_analysis['factors'].append('تقلبات عالية في السوق')
                
            # تحديد مستوى الثقة الإجمالي
            positive_factors = len([f for f in confidence_analysis['factors'] 
                                  if 'عالية' in f or 'قوي' in f or 'ممتاز' in f])
            negative_factors = len([f for f in confidence_analysis['factors'] 
                                  if 'ضعيف' in f or 'نقص' in f or 'تضارب' in f])
            
            if positive_factors > negative_factors:
                confidence_analysis['overall_confidence'] = 'عالي'
            elif negative_factors > positive_factors:
                confidence_analysis['overall_confidence'] = 'منخفض'
            
            return confidence_analysis
        
        except Exception as e:
            self.logger.error(f"خطأ في تحليل مستوى الثقة: {e}")
            return {'overall_confidence': 'غير محدد'}
    
    def _generate_price_recommendations(self, predictions: Dict, market_data: Dict) -> Dict:
        """توليد التوصيات بناءً على التنبؤات"""
        try:
            recommendations = {
                'short_term': 'انتظار',
                'medium_term': 'انتظار',
                'long_term': 'انتظار',
                'risk_level': 'متوسط',
                'entry_points': [],
                'exit_points': [],
                'stop_loss': None,
                'take_profit': None
            }
            
            if 'ensemble' not in predictions:
                return recommendations
            
            ensemble = predictions['ensemble']
            current_price = market_data.get('current_price', 0)
            
            # تحليل التوصيات قصيرة المدى (1-3 أيام)
            short_term_changes = []
            if '1_day' in ensemble:
                short_term_changes.append(ensemble['1_day']['price_change_percent'])
            if '3_day' in ensemble:
                short_term_changes.append(ensemble['3_day']['price_change_percent'])
            
            if short_term_changes:
                avg_short_change = np.mean(short_term_changes)
                if avg_short_change > 5:
                    recommendations['short_term'] = 'شراء قوي'
                elif avg_short_change > 2:
                    recommendations['short_term'] = 'شراء'
                elif avg_short_change < -5:
                    recommendations['short_term'] = 'بيع قوي'
                elif avg_short_change < -2:
                    recommendations['short_term'] = 'بيع'
            
            # تحليل التوصيات متوسطة المدى (7-14 يوم)
            medium_term_changes = []
            if '7_day' in ensemble:
                medium_term_changes.append(ensemble['7_day']['price_change_percent'])
            if '14_day' in ensemble:
                medium_term_changes.append(ensemble['14_day']['price_change_percent'])
            
            if medium_term_changes:
                avg_medium_change = np.mean(medium_term_changes)
                if avg_medium_change > 10:
                    recommendations['medium_term'] = 'شراء قوي'
                elif avg_medium_change > 5:
                    recommendations['medium_term'] = 'شراء'
                elif avg_medium_change < -10:
                    recommendations['medium_term'] = 'بيع قوي'
                elif avg_medium_change < -5:
                    recommendations['medium_term'] = 'بيع'
            
            # تحليل التوصيات طويلة المدى (30 يوم)
            if '30_day' in ensemble:
                long_change = ensemble['30_day']['price_change_percent']
                if long_change > 20:
                    recommendations['long_term'] = 'شراء قوي'
                elif long_change > 10:
                    recommendations['long_term'] = 'شراء'
                elif long_change < -20:
                    recommendations['long_term'] = 'بيع قوي'
                elif long_change < -10:
                    recommendations['long_term'] = 'بيع'
            
            # تحديد مستوى المخاطر
            volatility = market_data.get('price_change_percentage_24h', 0)
            if abs(volatility) > 10:
                recommendations['risk_level'] = 'عالي'
            elif abs(volatility) > 5:
                recommendations['risk_level'] = 'متوسط'
            else:
                recommendations['risk_level'] = 'منخفض'
            
            # نقاط الدخول والخروج
            if current_price > 0:
                # نقاط الدخول (أسعار أقل من الحالي)
                recommendations['entry_points'] = [
                    round(current_price * 0.95, 6),  # 5% أقل
                    round(current_price * 0.90, 6),  # 10% أقل
                    round(current_price * 0.85, 6)   # 15% أقل
                ]
                
                # نقاط الخروج (أسعار أعلى من الحالي)
                recommendations['exit_points'] = [
                    round(current_price * 1.10, 6),  # 10% أعلى
                    round(current_price * 1.20, 6),  # 20% أعلى
                    round(current_price * 1.30, 6)   # 30% أعلى
                ]
                
                # وقف الخسارة
                recommendations['stop_loss'] = round(current_price * 0.85, 6)  # 15% أقل
                
                # جني الأرباح
                recommendations['take_profit'] = round(current_price * 1.25, 6)  # 25% أعلى
            
            return recommendations
        
        except Exception as e:
            self.logger.error(f"خطأ في توليد التوصيات: {e}")
            return recommendations
    
    async def generate_comprehensive_prediction(self, coin_id: str, 
                                              historical_data: pd.DataFrame) -> Dict:
        """توليد تنبؤ شامل للعملة"""
        try:
            # الحصول على بيانات السوق الحالية
            from data_fetcher import CryptoDataFetcher
            
            async with CryptoDataFetcher() as fetcher:
                market_data = await fetcher.get_market_data([coin_id])
                coin_market_data = market_data.get(coin_id, {})
            
            # التنبؤ بالسعر
            price_prediction = await self.predict_price(coin_id, coin_market_data)
            
            # تحليل الاتجاهات
            trend_analysis = self._analyze_price_trends(historical_data)
            
            # تحليل المخاطر
            risk_analysis = self._analyze_prediction_risks(historical_data, price_prediction)
            
            # تحليل الفرص
            opportunity_analysis = self._analyze_opportunities(price_prediction, coin_market_data)
            
            return {
                'coin_id': coin_id,
                'analysis_time': datetime.now(),
                'price_prediction': price_prediction,
                'trend_analysis': trend_analysis,
                'risk_analysis': risk_analysis,
                'opportunity_analysis': opportunity_analysis,
                'overall_score': self._calculate_overall_prediction_score(
                    price_prediction, trend_analysis, risk_analysis
                )
            }
        
        except Exception as e:
            self.logger.error(f"خطأ في التنبؤ الشامل لـ {coin_id}: {e}")
            return {}
    
    def _analyze_price_trends(self, df: pd.DataFrame) -> Dict:
        """تحليل اتجاهات الأسعار"""
        try:
            if df.empty or 'price' not in df.columns:
                return {}
            
            prices = df['price'].values
            
            # اتجاه قصير المدى (آخر 7 أيام)
            short_trend = 'محايد'
            if len(prices) >= 7:
                recent_change = (prices[-1] - prices[-7]) / prices[-7] * 100
                if recent_change > 5:
                    short_trend = 'صاعد قوي'
                elif recent_change > 2:
                    short_trend = 'صاعد'
                elif recent_change < -5:
                    short_trend = 'هابط قوي'
                elif recent_change < -2:
                    short_trend = 'هابط'
            
            # اتجاه متوسط المدى (آخر 30 يوم)
            medium_trend = 'محايد'
            if len(prices) >= 30:
                medium_change = (prices[-1] - prices[-30]) / prices[-30] * 100
                if medium_change > 15:
                    medium_trend = 'صاعد قوي'
                elif medium_change > 5:
                    medium_trend = 'صاعد'
                elif medium_change < -15:
                    medium_trend = 'هابط قوي'
                elif medium_change < -5:
                    medium_trend = 'هابط'
            
            # قوة الاتجاه
            trend_strength = 'متوسط'
            if len(prices) >= 10:
                volatility = np.std(prices[-10:]) / np.mean(prices[-10:])
                if volatility < 0.02:
                    trend_strength = 'قوي'
                elif volatility > 0.05:
                    trend_strength = 'ضعيف'
            
            return {
                'short_term_trend': short_trend,
                'medium_term_trend': medium_trend,
                'trend_strength': trend_strength,
                'current_momentum': self._calculate_momentum(prices)
            }
        
        except Exception as e:
            self.logger.error(f"خطأ في تحليل الاتجاهات: {e}")
            return {}
    
    def _calculate_momentum(self, prices: np.ndarray) -> str:
        """حساب الزخم الحالي"""
        try:
            if len(prices) < 5:
                return 'غير محدد'
            
            # حساب معدل التغيير
            recent_roc = (prices[-1] - prices[-5]) / prices[-5] * 100
            
            if recent_roc > 3:
                return 'زخم صاعد قوي'
            elif recent_roc > 1:
                return 'زخم صاعد'
            elif recent_roc < -3:
                return 'زخم هابط قوي'
            elif recent_roc < -1:
                return 'زخم هابط'
            else:
                return 'زخم محايد'
        
        except:
            return 'غير محدد'
    
    def _analyze_prediction_risks(self, df: pd.DataFrame, prediction: Dict) -> Dict:
        """تحليل مخاطر التنبؤ"""
        try:
            risks = {
                'volatility_risk': 'متوسط',
                'prediction_uncertainty': 'متوسط',
                'market_risk': 'متوسط',
                'risk_factors': []
            }
            
            if df.empty:
                return risks
            
            # تحليل التقلبات
            if 'price' in df.columns:
                price_changes = df['price'].pct_change().dropna()
                volatility = price_changes.std()
                
                if volatility > 0.05:
                    risks['volatility_risk'] = 'عالي'
                    risks['risk_factors'].append('تقلبات عالية في الأسعار')
                elif volatility < 0.02:
                    risks['volatility_risk'] = 'منخفض'
            
            # تحليل عدم اليقين في التنبؤ
            if 'confidence' in prediction:
                confidence = prediction['confidence']
                overall_confidence = confidence.get('overall_confidence', 'متوسط')
                
                if overall_confidence == 'منخفض':
                    risks['prediction_uncertainty'] = 'عالي'
                    risks['risk_factors'].append('عدم يقين عالي في التنبؤات')
                elif overall_confidence == 'عالي':
                    risks['prediction_uncertainty'] = 'منخفض'
            
            return risks
        
        except Exception as e:
            self.logger.error(f"خطأ في تحليل المخاطر: {e}")
            return {'volatility_risk': 'غير محدد'}
    
    def _analyze_opportunities(self, prediction: Dict, market_data: Dict) -> Dict:
        """تحليل الفرص الاستثمارية"""
        try:
            opportunities = {
                'short_term_opportunity': 'محدود',
                'long_term_opportunity': 'محدود',
                'opportunity_factors': []
            }
            
            if 'predictions' not in prediction or 'ensemble' not in prediction['predictions']:
                return opportunities
            
            ensemble = prediction['predictions']['ensemble']
            
            # تحليل الفرص قصيرة المدى
            short_term_gains = []
            for period in ['1_day', '3_day', '7_day']:
                if period in ensemble:
                    change = ensemble[period]['price_change_percent']
                    short_term_gains.append(change)
            
            if short_term_gains:
                avg_short_gain = np.mean(short_term_gains)
                if avg_short_gain > 10:
                    opportunities['short_term_opportunity'] = 'ممتاز'
                    opportunities['opportunity_factors'].append('فرصة ربح قصيرة المدى ممتازة')
                elif avg_short_gain > 5:
                    opportunities['short_term_opportunity'] = 'جيد'
                    opportunities['opportunity_factors'].append('فرصة ربح قصيرة المدى جيدة')
            
            # تحليل الفرص طويلة المدى
            if '30_day' in ensemble:
                long_change = ensemble['30_day']['price_change_percent']
                if long_change > 25:
                    opportunities['long_term_opportunity'] = 'ممتاز'
                    opportunities['opportunity_factors'].append('فرصة ربح طويلة المدى ممتازة')
                elif long_change > 15:
                    opportunities['long_term_opportunity'] = 'جيد'
                    opportunities['opportunity_factors'].append('فرصة ربح طويلة المدى جيدة')
            
            return opportunities
        
        except Exception as e:
            self.logger.error(f"خطأ في تحليل الفرص: {e}")
            return {'short_term_opportunity': 'غير محدد'}
    
    def _calculate_overall_prediction_score(self, prediction: Dict, 
                                          trend_analysis: Dict, risk_analysis: Dict) -> Dict:
        """حساب النتيجة الإجمالية للتنبؤ"""
        try:
            score = 0
            max_score = 100
            
            # نقاط التنبؤ (40 نقطة)
            if 'predictions' in prediction and 'ensemble' in prediction['predictions']:
                ensemble = prediction['predictions']['ensemble']
                positive_predictions = sum(1 for pred in ensemble.values() 
                                         if pred['price_change_percent'] > 0)
                total_predictions = len(ensemble)
                
                if total_predictions > 0:
                    prediction_score = (positive_predictions / total_predictions) * 40
                    score += prediction_score
            
            # نقاط الاتجاه (30 نقطة)
            if trend_analysis:
                trend_score = 0
                if trend_analysis.get('short_term_trend') in ['صاعد', 'صاعد قوي']:
                    trend_score += 10
                if trend_analysis.get('medium_term_trend') in ['صاعد', 'صاعد قوي']:
                    trend_score += 15
                if trend_analysis.get('trend_strength') == 'قوي':
                    trend_score += 5
                
                score += trend_score
            
            # نقاط المخاطر (30 نقطة - معكوسة)
            if risk_analysis:
                risk_score = 30
                if risk_analysis.get('volatility_risk') == 'عالي':
                    risk_score -= 15
                elif risk_analysis.get('volatility_risk') == 'منخفض':
                    risk_score -= 5
                
                if risk_analysis.get('prediction_uncertainty') == 'عالي':
                    risk_score -= 10
                elif risk_analysis.get('prediction_uncertainty') == 'منخفض':
                    risk_score -= 0
                
                score += max(0, risk_score)
            
            # تحويل إلى نسبة مئوية
            percentage = (score / max_score) * 100
            
            # تحديد التصنيف
            if percentage >= 80:
                rating = 'ممتاز'
            elif percentage >= 60:
                rating = 'جيد'
            elif percentage >= 40:
                rating = 'متوسط'
            elif percentage >= 20:
                rating = 'ضعيف'
            else:
                rating = 'ضعيف جداً'
            
            return {
                'score': round(score, 1),
                'percentage': round(percentage, 1),
                'rating': rating,
                'recommendation': self._get_score_recommendation(percentage)
            }
        
        except Exception as e:
            self.logger.error(f"خطأ في حساب النتيجة الإجمالية: {e}")
            return {'score': 0, 'percentage': 0, 'rating': 'غير محدد'}
    
    def _get_score_recommendation(self, percentage: float) -> str:
        """الحصول على التوصية بناءً على النتيجة"""
        if percentage >= 80:
            return 'استثمار قوي - فرصة ممتازة'
        elif percentage >= 60:
            return 'استثمار جيد - فرصة واعدة'
        elif percentage >= 40:
            return 'استثمار حذر - مراقبة مطلوبة'
        elif percentage >= 20:
            return 'تجنب الاستثمار - مخاطر عالية'
        else:
            return 'تجنب تماماً - مخاطر عالية جداً'