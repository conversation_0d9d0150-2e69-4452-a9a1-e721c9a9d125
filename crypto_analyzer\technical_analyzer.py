"""
وحدة التحليل الفني للعملات الرقمية
Technical Analysis Module for Cryptocurrencies
"""

import pandas as pd
import numpy as np
# import talib  # مؤقتاً معطل
from typing import Dict, List, Tuple, Optional
import logging
from datetime import datetime, timedelta

class TechnicalAnalyzer:
    """فئة التحليل الفني للعملات الرقمية"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # إعدادات المؤشرات الفنية
        self.indicators_config = {
            'sma_periods': [5, 10, 20, 50, 100, 200],
            'ema_periods': [12, 26, 50, 100],
            'rsi_period': 14,
            'macd_fast': 12,
            'macd_slow': 26,
            'macd_signal': 9,
            'bb_period': 20,
            'bb_std': 2,
            'stoch_k': 14,
            'stoch_d': 3,
            'adx_period': 14,
            'cci_period': 20,
            'williams_r_period': 14,
            'atr_period': 14
        }
    
    async def analyze(self, coin_id: str, market_data: Dict) -> Dict:
        """تحليل فني شامل للعملة"""
        try:
            # الحصول على البيانات التاريخية
            from data_fetcher import CryptoDataFetcher
            
            async with CryptoDataFetcher() as fetcher:
                df = await fetcher.get_historical_data(coin_id, days=200)
                ohlcv_df = await fetcher.get_ohlcv_data(coin_id, days=100)
            
            if df.empty or ohlcv_df.empty:
                self.logger.warning(f"لا توجد بيانات كافية للتحليل الفني لـ {coin_id}")
                return {}
            
            # دمج البيانات
            combined_df = self._combine_data(df, ohlcv_df)
            
            # حساب المؤشرات الفنية
            indicators = self._calculate_all_indicators(combined_df)
            
            # تحليل الاتجاهات
            trends = self._analyze_trends(combined_df, indicators)
            
            # تحليل الدعم والمقاومة
            support_resistance = self._analyze_support_resistance(combined_df)
            
            # تحليل الأنماط
            patterns = self._analyze_patterns(combined_df)
            
            # تحليل الحجم
            volume_analysis = self._analyze_volume(combined_df)
            
            # إشارات التداول
            signals = self._generate_trading_signals(indicators, trends, patterns)
            
            # تقييم القوة الإجمالية
            strength_score = self._calculate_strength_score(indicators, trends, signals)
            
            return {
                'coin_id': coin_id,
                'timestamp': datetime.now(),
                'current_price': market_data.get('current_price', 0),
                'indicators': indicators,
                'trends': trends,
                'support_resistance': support_resistance,
                'patterns': patterns,
                'volume_analysis': volume_analysis,
                'signals': signals,
                'strength_score': strength_score,
                'recommendation': self._get_recommendation(strength_score, signals)
            }
        
        except Exception as e:
            self.logger.error(f"خطأ في التحليل الفني لـ {coin_id}: {e}")
            return {}
    
    def _combine_data(self, price_df: pd.DataFrame, ohlcv_df: pd.DataFrame) -> pd.DataFrame:
        """دمج بيانات الأسعار مع بيانات OHLCV"""
        try:
            # إعادة تسمية الأعمدة
            price_df = price_df.rename(columns={'price': 'close'})
            
            # دمج البيانات
            combined = pd.merge(ohlcv_df, price_df[['volume', 'market_cap']], 
                              left_index=True, right_index=True, how='left')
            
            # ملء القيم المفقودة
            combined = combined.fillna(method='forward').fillna(method='backward')
            
            return combined
        
        except Exception as e:
            self.logger.error(f"خطأ في دمج البيانات: {e}")
            return pd.DataFrame()
    
    def _calculate_all_indicators(self, df: pd.DataFrame) -> Dict:
        """حساب جميع المؤشرات الفنية"""
        indicators = {}
        
        try:
            # التأكد من وجود البيانات المطلوبة
            if len(df) < 200:
                self.logger.warning("البيانات غير كافية لحساب جميع المؤشرات")
            
            close = df['close'].values
            high = df['high'].values
            low = df['low'].values
            volume = df['volume'].values if 'volume' in df.columns else np.zeros(len(close))
            
            # المتوسطات المتحركة البسيطة (SMA)
            indicators['sma'] = {}
            for period in self.indicators_config['sma_periods']:
                if len(close) >= period:
                    indicators['sma'][f'sma_{period}'] = talib.SMA(close, timeperiod=period)[-1]
            
            # المتوسطات المتحركة الأسية (EMA)
            indicators['ema'] = {}
            for period in self.indicators_config['ema_periods']:
                if len(close) >= period:
                    indicators['ema'][f'ema_{period}'] = talib.EMA(close, timeperiod=period)[-1]
            
            # مؤشر القوة النسبية (RSI)
            if len(close) >= self.indicators_config['rsi_period']:
                rsi_values = talib.RSI(close, timeperiod=self.indicators_config['rsi_period'])
                indicators['rsi'] = {
                    'current': rsi_values[-1],
                    'signal': self._interpret_rsi(rsi_values[-1])
                }
            
            # MACD
            if len(close) >= self.indicators_config['macd_slow']:
                macd, macd_signal, macd_hist = talib.MACD(
                    close,
                    fastperiod=self.indicators_config['macd_fast'],
                    slowperiod=self.indicators_config['macd_slow'],
                    signalperiod=self.indicators_config['macd_signal']
                )
                indicators['macd'] = {
                    'macd': macd[-1],
                    'signal': macd_signal[-1],
                    'histogram': macd_hist[-1],
                    'interpretation': self._interpret_macd(macd[-1], macd_signal[-1], macd_hist[-1])
                }
            
            # Bollinger Bands
            if len(close) >= self.indicators_config['bb_period']:
                bb_upper, bb_middle, bb_lower = talib.BBANDS(
                    close,
                    timeperiod=self.indicators_config['bb_period'],
                    nbdevup=self.indicators_config['bb_std'],
                    nbdevdn=self.indicators_config['bb_std']
                )
                indicators['bollinger_bands'] = {
                    'upper': bb_upper[-1],
                    'middle': bb_middle[-1],
                    'lower': bb_lower[-1],
                    'position': self._interpret_bb_position(close[-1], bb_upper[-1], bb_lower[-1])
                }
            
            # Stochastic Oscillator
            if len(close) >= self.indicators_config['stoch_k']:
                slowk, slowd = talib.STOCH(
                    high, low, close,
                    fastk_period=self.indicators_config['stoch_k'],
                    slowk_period=self.indicators_config['stoch_d'],
                    slowd_period=self.indicators_config['stoch_d']
                )
                indicators['stochastic'] = {
                    'k': slowk[-1],
                    'd': slowd[-1],
                    'signal': self._interpret_stochastic(slowk[-1], slowd[-1])
                }
            
            # ADX (Average Directional Index)
            if len(close) >= self.indicators_config['adx_period']:
                adx = talib.ADX(high, low, close, timeperiod=self.indicators_config['adx_period'])
                plus_di = talib.PLUS_DI(high, low, close, timeperiod=self.indicators_config['adx_period'])
                minus_di = talib.MINUS_DI(high, low, close, timeperiod=self.indicators_config['adx_period'])
                
                indicators['adx'] = {
                    'adx': adx[-1],
                    'plus_di': plus_di[-1],
                    'minus_di': minus_di[-1],
                    'trend_strength': self._interpret_adx(adx[-1])
                }
            
            # CCI (Commodity Channel Index)
            if len(close) >= self.indicators_config['cci_period']:
                cci = talib.CCI(high, low, close, timeperiod=self.indicators_config['cci_period'])
                indicators['cci'] = {
                    'current': cci[-1],
                    'signal': self._interpret_cci(cci[-1])
                }
            
            # Williams %R
            if len(close) >= self.indicators_config['williams_r_period']:
                willr = talib.WILLR(high, low, close, timeperiod=self.indicators_config['williams_r_period'])
                indicators['williams_r'] = {
                    'current': willr[-1],
                    'signal': self._interpret_williams_r(willr[-1])
                }
            
            # ATR (Average True Range)
            if len(close) >= self.indicators_config['atr_period']:
                atr = talib.ATR(high, low, close, timeperiod=self.indicators_config['atr_period'])
                indicators['atr'] = {
                    'current': atr[-1],
                    'volatility_level': self._interpret_atr(atr[-1], close[-1])
                }
            
            # مؤشرات الحجم
            if len(volume) > 0 and np.sum(volume) > 0:
                indicators['volume'] = self._calculate_volume_indicators(close, volume)
            
            return indicators
        
        except Exception as e:
            self.logger.error(f"خطأ في حساب المؤشرات الفنية: {e}")
            return {}
    
    def _interpret_rsi(self, rsi: float) -> str:
        """تفسير مؤشر RSI"""
        if rsi >= 70:
            return "ذروة شراء - إشارة بيع محتملة"
        elif rsi <= 30:
            return "ذروة بيع - إشارة شراء محتملة"
        elif rsi >= 50:
            return "اتجاه صاعد"
        else:
            return "اتجاه هابط"
    
    def _interpret_macd(self, macd: float, signal: float, histogram: float) -> str:
        """تفسير مؤشر MACD"""
        if macd > signal and histogram > 0:
            return "إشارة شراء قوية"
        elif macd < signal and histogram < 0:
            return "إشارة بيع قوية"
        elif macd > signal:
            return "اتجاه صاعد"
        else:
            return "اتجاه هابط"
    
    def _interpret_bb_position(self, price: float, upper: float, lower: float) -> str:
        """تفسير موقع السعر في نطاقات بولينجر"""
        if price >= upper:
            return "أعلى النطاق العلوي - ذروة شراء"
        elif price <= lower:
            return "أسفل النطاق السفلي - ذروة بيع"
        elif price > (upper + lower) / 2:
            return "النصف العلوي - اتجاه صاعد"
        else:
            return "النصف السفلي - اتجاه هابط"
    
    def _interpret_stochastic(self, k: float, d: float) -> str:
        """تفسير مؤشر Stochastic"""
        if k >= 80 and d >= 80:
            return "ذروة شراء"
        elif k <= 20 and d <= 20:
            return "ذروة بيع"
        elif k > d:
            return "زخم صاعد"
        else:
            return "زخم هابط"
    
    def _interpret_adx(self, adx: float) -> str:
        """تفسير مؤشر ADX"""
        if adx >= 50:
            return "اتجاه قوي جداً"
        elif adx >= 25:
            return "اتجاه قوي"
        elif adx >= 20:
            return "اتجاه متوسط"
        else:
            return "اتجاه ضعيف أو عدم وجود اتجاه"
    
    def _interpret_cci(self, cci: float) -> str:
        """تفسير مؤشر CCI"""
        if cci >= 100:
            return "ذروة شراء"
        elif cci <= -100:
            return "ذروة بيع"
        elif cci > 0:
            return "اتجاه صاعد"
        else:
            return "اتجاه هابط"
    
    def _interpret_williams_r(self, willr: float) -> str:
        """تفسير مؤشر Williams %R"""
        if willr >= -20:
            return "ذروة شراء"
        elif willr <= -80:
            return "ذروة بيع"
        elif willr >= -50:
            return "اتجاه صاعد"
        else:
            return "اتجاه هابط"
    
    def _interpret_atr(self, atr: float, price: float) -> str:
        """تفسير مؤشر ATR"""
        volatility_percentage = (atr / price) * 100
        
        if volatility_percentage >= 5:
            return "تقلبات عالية جداً"
        elif volatility_percentage >= 3:
            return "تقلبات عالية"
        elif volatility_percentage >= 1.5:
            return "تقلبات متوسطة"
        else:
            return "تقلبات منخفضة"
    
    def _calculate_volume_indicators(self, close: np.ndarray, volume: np.ndarray) -> Dict:
        """حساب مؤشرات الحجم"""
        try:
            # On-Balance Volume (OBV)
            obv = talib.OBV(close, volume)
            
            # Volume Rate of Change
            volume_roc = talib.ROC(volume, timeperiod=10)
            
            # Average Volume
            avg_volume = talib.SMA(volume, timeperiod=20)
            
            return {
                'obv': obv[-1],
                'volume_roc': volume_roc[-1],
                'avg_volume': avg_volume[-1],
                'current_volume': volume[-1],
                'volume_ratio': volume[-1] / avg_volume[-1] if avg_volume[-1] > 0 else 0
            }
        
        except Exception as e:
            self.logger.error(f"خطأ في حساب مؤشرات الحجم: {e}")
            return {}
    
    def _analyze_trends(self, df: pd.DataFrame, indicators: Dict) -> Dict:
        """تحليل الاتجاهات"""
        try:
            trends = {}
            close = df['close'].values
            
            # اتجاه قصير المدى (5-20 يوم)
            if 'sma' in indicators and 'sma_5' in indicators['sma'] and 'sma_20' in indicators['sma']:
                if indicators['sma']['sma_5'] > indicators['sma']['sma_20']:
                    trends['short_term'] = "صاعد"
                else:
                    trends['short_term'] = "هابط"
            
            # اتجاه متوسط المدى (20-50 يوم)
            if 'sma' in indicators and 'sma_20' in indicators['sma'] and 'sma_50' in indicators['sma']:
                if indicators['sma']['sma_20'] > indicators['sma']['sma_50']:
                    trends['medium_term'] = "صاعد"
                else:
                    trends['medium_term'] = "هابط"
            
            # اتجاه طويل المدى (50-200 يوم)
            if 'sma' in indicators and 'sma_50' in indicators['sma'] and 'sma_200' in indicators['sma']:
                if indicators['sma']['sma_50'] > indicators['sma']['sma_200']:
                    trends['long_term'] = "صاعد"
                else:
                    trends['long_term'] = "هابط"
            
            # قوة الاتجاه
            if 'adx' in indicators:
                trends['strength'] = indicators['adx']['trend_strength']
            
            return trends
        
        except Exception as e:
            self.logger.error(f"خطأ في تحليل الاتجاهات: {e}")
            return {}
    
    def _analyze_support_resistance(self, df: pd.DataFrame) -> Dict:
        """تحليل مستويات الدعم والمقاومة"""
        try:
            close = df['close'].values
            high = df['high'].values
            low = df['low'].values
            
            # حساب مستويات الدعم والمقاومة باستخدام Pivot Points
            recent_high = np.max(high[-20:])
            recent_low = np.min(low[-20:])
            recent_close = close[-1]
            
            # Pivot Point
            pivot = (recent_high + recent_low + recent_close) / 3
            
            # مستويات المقاومة
            r1 = 2 * pivot - recent_low
            r2 = pivot + (recent_high - recent_low)
            r3 = recent_high + 2 * (pivot - recent_low)
            
            # مستويات الدعم
            s1 = 2 * pivot - recent_high
            s2 = pivot - (recent_high - recent_low)
            s3 = recent_low - 2 * (recent_high - pivot)
            
            return {
                'pivot_point': pivot,
                'resistance_levels': [r1, r2, r3],
                'support_levels': [s1, s2, s3],
                'current_price': recent_close,
                'nearest_resistance': min([r for r in [r1, r2, r3] if r > recent_close], default=r1),
                'nearest_support': max([s for s in [s1, s2, s3] if s < recent_close], default=s1)
            }
        
        except Exception as e:
            self.logger.error(f"خطأ في تحليل الدعم والمقاومة: {e}")
            return {}
    
    def _analyze_patterns(self, df: pd.DataFrame) -> Dict:
        """تحليل الأنماط الفنية"""
        try:
            patterns = {}
            
            if len(df) < 50:
                return patterns
            
            open_prices = df['open'].values
            high_prices = df['high'].values
            low_prices = df['low'].values
            close_prices = df['close'].values
            
            # أنماط الشموع
            patterns['candlestick'] = {}
            
            # Doji
            doji = talib.CDLDOJI(open_prices, high_prices, low_prices, close_prices)
            if doji[-1] != 0:
                patterns['candlestick']['doji'] = "نمط دوجي - تردد في السوق"
            
            # Hammer
            hammer = talib.CDLHAMMER(open_prices, high_prices, low_prices, close_prices)
            if hammer[-1] != 0:
                patterns['candlestick']['hammer'] = "نمط المطرقة - إشارة انعكاس صاعدة محتملة"
            
            # Shooting Star
            shooting_star = talib.CDLSHOOTINGSTAR(open_prices, high_prices, low_prices, close_prices)
            if shooting_star[-1] != 0:
                patterns['candlestick']['shooting_star'] = "نمط النجمة الساقطة - إشارة انعكاس هابطة محتملة"
            
            # Engulfing Pattern
            engulfing = talib.CDLENGULFING(open_prices, high_prices, low_prices, close_prices)
            if engulfing[-1] > 0:
                patterns['candlestick']['bullish_engulfing'] = "نمط الابتلاع الصاعد - إشارة شراء قوية"
            elif engulfing[-1] < 0:
                patterns['candlestick']['bearish_engulfing'] = "نمط الابتلاع الهابط - إشارة بيع قوية"
            
            return patterns
        
        except Exception as e:
            self.logger.error(f"خطأ في تحليل الأنماط: {e}")
            return {}
    
    def _analyze_volume(self, df: pd.DataFrame) -> Dict:
        """تحليل الحجم"""
        try:
            if 'volume' not in df.columns:
                return {}
            
            volume = df['volume'].values
            close = df['close'].values
            
            # متوسط الحجم
            avg_volume_20 = np.mean(volume[-20:])
            current_volume = volume[-1]
            
            # نسبة الحجم الحالي إلى المتوسط
            volume_ratio = current_volume / avg_volume_20 if avg_volume_20 > 0 else 0
            
            # تحليل العلاقة بين السعر والحجم
            price_change = (close[-1] - close[-2]) / close[-2] * 100
            volume_change = (volume[-1] - volume[-2]) / volume[-2] * 100 if volume[-2] > 0 else 0
            
            analysis = {
                'current_volume': current_volume,
                'avg_volume_20': avg_volume_20,
                'volume_ratio': volume_ratio,
                'volume_trend': "مرتفع" if volume_ratio > 1.5 else "متوسط" if volume_ratio > 0.8 else "منخفض",
                'price_volume_correlation': self._interpret_price_volume(price_change, volume_change)
            }
            
            return analysis
        
        except Exception as e:
            self.logger.error(f"خطأ في تحليل الحجم: {e}")
            return {}
    
    def _interpret_price_volume(self, price_change: float, volume_change: float) -> str:
        """تفسير العلاقة بين السعر والحجم"""
        if price_change > 0 and volume_change > 0:
            return "ارتفاع السعر مع زيادة الحجم - إشارة إيجابية قوية"
        elif price_change < 0 and volume_change > 0:
            return "انخفاض السعر مع زيادة الحجم - ضغط بيع قوي"
        elif price_change > 0 and volume_change < 0:
            return "ارتفاع السعر مع انخفاض الحجم - إشارة ضعيفة"
        elif price_change < 0 and volume_change < 0:
            return "انخفاض السعر مع انخفاض الحجم - ضغط بيع ضعيف"
        else:
            return "حركة محايدة"
    
    def _generate_trading_signals(self, indicators: Dict, trends: Dict, patterns: Dict) -> Dict:
        """توليد إشارات التداول"""
        signals = {
            'buy_signals': [],
            'sell_signals': [],
            'hold_signals': [],
            'overall_signal': 'HOLD'
        }
        
        try:
            buy_score = 0
            sell_score = 0
            
            # إشارات RSI
            if 'rsi' in indicators:
                rsi_value = indicators['rsi']['current']
                if rsi_value <= 30:
                    signals['buy_signals'].append("RSI في ذروة البيع")
                    buy_score += 2
                elif rsi_value >= 70:
                    signals['sell_signals'].append("RSI في ذروة الشراء")
                    sell_score += 2
            
            # إشارات MACD
            if 'macd' in indicators:
                macd_interp = indicators['macd']['interpretation']
                if "شراء" in macd_interp:
                    signals['buy_signals'].append("MACD إشارة شراء")
                    buy_score += 2
                elif "بيع" in macd_interp:
                    signals['sell_signals'].append("MACD إشارة بيع")
                    sell_score += 2
            
            # إشارات الاتجاه
            if trends:
                bullish_trends = sum(1 for trend in trends.values() if trend == "صاعد")
                bearish_trends = sum(1 for trend in trends.values() if trend == "هابط")
                
                if bullish_trends > bearish_trends:
                    signals['buy_signals'].append("الاتجاه العام صاعد")
                    buy_score += 1
                elif bearish_trends > bullish_trends:
                    signals['sell_signals'].append("الاتجاه العام هابط")
                    sell_score += 1
            
            # إشارات الأنماط
            if 'candlestick' in patterns:
                for pattern, description in patterns['candlestick'].items():
                    if "شراء" in description or "صاعد" in description:
                        signals['buy_signals'].append(f"نمط الشموع: {description}")
                        buy_score += 1
                    elif "بيع" in description or "هابط" in description:
                        signals['sell_signals'].append(f"نمط الشموع: {description}")
                        sell_score += 1
            
            # تحديد الإشارة الإجمالية
            if buy_score > sell_score + 1:
                signals['overall_signal'] = 'BUY'
            elif sell_score > buy_score + 1:
                signals['overall_signal'] = 'SELL'
            else:
                signals['overall_signal'] = 'HOLD'
                signals['hold_signals'].append("الإشارات متضاربة - انتظار تأكيد")
            
            signals['buy_score'] = buy_score
            signals['sell_score'] = sell_score
            
            return signals
        
        except Exception as e:
            self.logger.error(f"خطأ في توليد إشارات التداول: {e}")
            return signals
    
    def _calculate_strength_score(self, indicators: Dict, trends: Dict, signals: Dict) -> Dict:
        """حساب نقاط القوة الإجمالية"""
        try:
            total_score = 0
            max_score = 0
            
            # نقاط المؤشرات الفنية
            if 'rsi' in indicators:
                max_score += 10
                rsi = indicators['rsi']['current']
                if 30 <= rsi <= 70:
                    total_score += 8  # منطقة صحية
                elif 20 <= rsi <= 80:
                    total_score += 5  # منطقة مقبولة
                else:
                    total_score += 2  # ذروة شراء/بيع
            
            # نقاط الاتجاه
            if trends:
                max_score += 15
                bullish_trends = sum(1 for trend in trends.values() if trend == "صاعد")
                total_trends = len([t for t in trends.values() if t in ["صاعد", "هابط"]])
                
                if total_trends > 0:
                    trend_score = (bullish_trends / total_trends) * 15
                    total_score += trend_score
            
            # نقاط الإشارات
            max_score += 10
            buy_score = signals.get('buy_score', 0)
            sell_score = signals.get('sell_score', 0)
            
            if buy_score > sell_score:
                total_score += min(10, buy_score * 2)
            elif sell_score > buy_score:
                total_score += max(0, 10 - sell_score * 2)
            else:
                total_score += 5
            
            # حساب النسبة المئوية
            strength_percentage = (total_score / max_score * 100) if max_score > 0 else 0
            
            return {
                'total_score': total_score,
                'max_score': max_score,
                'percentage': strength_percentage,
                'level': self._get_strength_level(strength_percentage)
            }
        
        except Exception as e:
            self.logger.error(f"خطأ في حساب نقاط القوة: {e}")
            return {'percentage': 0, 'level': 'غير محدد'}
    
    def _get_strength_level(self, percentage: float) -> str:
        """تحديد مستوى القوة"""
        if percentage >= 80:
            return "قوي جداً"
        elif percentage >= 60:
            return "قوي"
        elif percentage >= 40:
            return "متوسط"
        elif percentage >= 20:
            return "ضعيف"
        else:
            return "ضعيف جداً"
    
    def _get_recommendation(self, strength_score: Dict, signals: Dict) -> str:
        """الحصول على التوصية النهائية"""
        try:
            signal = signals.get('overall_signal', 'HOLD')
            strength = strength_score.get('percentage', 0)
            
            if signal == 'BUY' and strength >= 60:
                return "شراء قوي - فرصة استثمارية ممتازة"
            elif signal == 'BUY' and strength >= 40:
                return "شراء - فرصة استثمارية جيدة"
            elif signal == 'BUY':
                return "شراء حذر - مراقبة مطلوبة"
            elif signal == 'SELL' and strength <= 40:
                return "بيع قوي - تجنب الاستثمار"
            elif signal == 'SELL':
                return "بيع - خروج من المركز"
            else:
                return "انتظار - مراقبة التطورات"
        
        except Exception as e:
            self.logger.error(f"خطأ في تحديد التوصية: {e}")
            return "غير محدد"