@echo off
chcp 65001 >nul
title أداة تحليل العملات الرقمية - التشغيل السريع

cls
echo.
echo        ████████████████████████████████████████████████████████
echo        █                                                      █
echo        █    🚀 أداة تحليل العملات الرقمية المتقدمة 🚀      █
echo        █                                                      █
echo        █           مرحباً بك في التشغيل السريع              █
echo        █                                                      █
echo        ████████████████████████████████████████████████████████
echo.
echo 🔍 جاري فحص النظام...

REM التحقق من Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo ❌ Python غير مثبت!
    echo.
    echo 💡 لتثبيت Python:
    echo    1. اذه<PERSON> إلى https://python.org
    echo    2. حمل Python 3.8 أو أحدث
    echo    3. تأكد من تحديد "Add Python to PATH" أثناء التثبيت
    echo.
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo.

REM تثبيت المتطلبات تلقائياً
echo 🔧 تثبيت/تحديث المتطلبات...
pip install -r requirements.txt --quiet --disable-pip-version-check

if %errorlevel% neq 0 (
    echo.
    echo ⚠️  تحذير: قد تكون بعض المكتبات غير مثبتة بشكل صحيح
    echo 💡 تأكد من اتصال الإنترنت
    echo.
)

echo ✅ تم التحقق من المتطلبات
echo.
echo 🚀 بدء التشغيل...
echo.

REM تشغيل البرنامج مع معالجة الأخطاء
python quick_start.py

REM في حالة حدوث خطأ
if %errorlevel% neq 0 (
    echo.
    echo ❌ حدث خطأ أثناء التشغيل
    echo.
    echo 🔧 حلول مقترحة:
    echo    1. تأكد من اتصال الإنترنت
    echo    2. شغل الملف كمدير (Run as Administrator)
    echo    3. تأكد من تثبيت جميع المتطلبات
    echo.
    echo 📞 للدعم: راجع ملف README.md
    echo.
)

echo.
echo 👋 شكراً لاستخدام أداة تحليل العملات الرقمية
echo 💡 يمكنك إغلاق هذه النافذة الآن
echo.
pause