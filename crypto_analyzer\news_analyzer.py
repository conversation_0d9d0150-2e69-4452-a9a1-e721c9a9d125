"""
وحدة تحليل الأخبار والمشاعر للعملات الرقمية
News and Sentiment Analysis Module for Cryptocurrencies
"""

import aiohttp
import asyncio
import logging
from typing import Dict, List, Optional
from datetime import datetime, timedelta
import re
import json
from textblob import TextBlob
import feedparser
from bs4 import BeautifulSoup

class NewsAnalyzer:
    """فئة تحليل الأخبار والمشاعر"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # مصادر الأخبار
        self.news_sources = {
            'coindesk': {
                'rss': 'https://www.coindesk.com/arc/outboundfeeds/rss/',
                'api': 'https://www.coindesk.com/api/v1/articles',
                'weight': 0.9
            },
            'cointelegraph': {
                'rss': 'https://cointelegraph.com/rss',
                'weight': 0.8
            },
            'cryptonews': {
                'rss': 'https://cryptonews.com/news/feed',
                'weight': 0.7
            },
            'decrypt': {
                'rss': 'https://decrypt.co/feed',
                'weight': 0.8
            },
            'newsapi': {
                'api': 'https://newsapi.org/v2/everything',
                'key': 'YOUR_NEWS_API_KEY',  # يحتاج إلى مفتاح API
                'weight': 0.6
            }
        }
        
        # الكلمات المفتاحية للعملات الرقمية
        self.crypto_keywords = {
            'bitcoin': ['bitcoin', 'btc', 'satoshi'],
            'ethereum': ['ethereum', 'eth', 'ether', 'vitalik'],
            'binancecoin': ['binance', 'bnb', 'binance coin'],
            'cardano': ['cardano', 'ada', 'charles hoskinson'],
            'solana': ['solana', 'sol'],
            'polkadot': ['polkadot', 'dot', 'gavin wood'],
            'dogecoin': ['dogecoin', 'doge', 'elon musk'],
            'avalanche-2': ['avalanche', 'avax'],
            'chainlink': ['chainlink', 'link'],
            'polygon': ['polygon', 'matic']
        }
        
        # كلمات المشاعر الإيجابية والسلبية
        self.sentiment_keywords = {
            'positive': [
                'bullish', 'surge', 'rally', 'pump', 'moon', 'adoption',
                'breakthrough', 'partnership', 'upgrade', 'launch',
                'positive', 'growth', 'increase', 'rise', 'gain'
            ],
            'negative': [
                'bearish', 'crash', 'dump', 'fall', 'decline', 'drop',
                'hack', 'scam', 'regulation', 'ban', 'negative',
                'loss', 'decrease', 'plunge', 'collapse'
            ]
        }
        
        self.session = None
    
    async def __aenter__(self):
        """إنشاء جلسة HTTP"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """إغلاق جلسة HTTP"""
        if self.session:
            await self.session.close()
    
    async def fetch_crypto_news(self, hours_back: int = 24) -> List[Dict]:
        """جلب الأخبار الحديثة للعملات الرقمية"""
        if not self.session:
            self.session = aiohttp.ClientSession()
        
        all_news = []
        
        try:
            # جلب الأخبار من مصادر RSS
            rss_tasks = []
            for source_name, source_info in self.news_sources.items():
                if 'rss' in source_info:
                    rss_tasks.append(self._fetch_rss_news(source_name, source_info))
            
            rss_results = await asyncio.gather(*rss_tasks, return_exceptions=True)
            
            for result in rss_results:
                if isinstance(result, list):
                    all_news.extend(result)
                elif isinstance(result, Exception):
                    self.logger.error(f"خطأ في جلب أخبار RSS: {result}")
            
            # تصفية الأخبار حسب التاريخ
            cutoff_time = datetime.now() - timedelta(hours=hours_back)
            filtered_news = [
                news for news in all_news 
                if news.get('published_date', datetime.min) > cutoff_time
            ]
            
            # ترتيب حسب التاريخ والأهمية
            filtered_news.sort(
                key=lambda x: (x.get('published_date', datetime.min), x.get('importance', 0)),
                reverse=True
            )
            
            self.logger.info(f"تم جلب {len(filtered_news)} خبر من {len(self.news_sources)} مصدر")
            return filtered_news[:50]  # أحدث 50 خبر
        
        except Exception as e:
            self.logger.error(f"خطأ في جلب الأخبار: {e}")
            return []
    
    async def _fetch_rss_news(self, source_name: str, source_info: Dict) -> List[Dict]:
        """جلب الأخبار من مصدر RSS"""
        try:
            rss_url = source_info['rss']
            weight = source_info.get('weight', 0.5)
            
            async with self.session.get(rss_url) as response:
                if response.status == 200:
                    rss_content = await response.text()
                    feed = feedparser.parse(rss_content)
                    
                    news_items = []
                    for entry in feed.entries[:20]:  # أحدث 20 خبر من كل مصدر
                        try:
                            # تحليل التاريخ
                            published_date = datetime.now()
                            if hasattr(entry, 'published_parsed') and entry.published_parsed:
                                published_date = datetime(*entry.published_parsed[:6])
                            
                            # استخراج النص
                            content = self._extract_text_content(
                                getattr(entry, 'summary', '') or getattr(entry, 'description', '')
                            )
                            
                            news_item = {
                                'title': getattr(entry, 'title', ''),
                                'content': content,
                                'url': getattr(entry, 'link', ''),
                                'source': source_name,
                                'published_date': published_date,
                                'importance': weight,
                                'raw_entry': entry
                            }
                            
                            # تحديد إذا كان الخبر متعلق بالعملات الرقمية
                            if self._is_crypto_related(news_item['title'] + ' ' + news_item['content']):
                                news_items.append(news_item)
                        
                        except Exception as e:
                            self.logger.warning(f"خطأ في معالجة خبر من {source_name}: {e}")
                            continue
                    
                    return news_items
                else:
                    self.logger.error(f"خطأ في جلب RSS من {source_name}: {response.status}")
                    return []
        
        except Exception as e:
            self.logger.error(f"خطأ في جلب أخبار RSS من {source_name}: {e}")
            return []
    
    def _extract_text_content(self, html_content: str) -> str:
        """استخراج النص من محتوى HTML"""
        try:
            if not html_content:
                return ""
            
            # إزالة HTML tags
            soup = BeautifulSoup(html_content, 'html.parser')
            text = soup.get_text()
            
            # تنظيف النص
            text = re.sub(r'\s+', ' ', text)  # إزالة المسافات الزائدة
            text = text.strip()
            
            return text[:1000]  # أول 1000 حرف
        
        except Exception as e:
            self.logger.warning(f"خطأ في استخراج النص: {e}")
            return html_content[:500] if html_content else ""
    
    def _is_crypto_related(self, text: str) -> bool:
        """تحديد إذا كان النص متعلق بالعملات الرقمية"""
        text_lower = text.lower()
        
        # البحث عن الكلمات المفتاحية
        crypto_terms = [
            'bitcoin', 'ethereum', 'crypto', 'blockchain', 'defi',
            'nft', 'altcoin', 'trading', 'exchange', 'wallet',
            'mining', 'staking', 'yield', 'token', 'coin'
        ]
        
        return any(term in text_lower for term in crypto_terms)
    
    async def analyze_sentiment(self, text: str) -> Dict:
        """تحليل المشاعر للنص"""
        try:
            if not text:
                return {'polarity': 0, 'subjectivity': 0, 'compound': 0, 'label': 'محايد'}
            
            # استخدام TextBlob للتحليل الأساسي
            blob = TextBlob(text)
            polarity = blob.sentiment.polarity
            subjectivity = blob.sentiment.subjectivity
            
            # تحليل متقدم باستخدام الكلمات المفتاحية
            keyword_sentiment = self._analyze_keyword_sentiment(text)
            
            # دمج النتائج
            compound_score = (polarity + keyword_sentiment) / 2
            
            # تحديد التصنيف
            if compound_score >= 0.1:
                label = 'إيجابي'
            elif compound_score <= -0.1:
                label = 'سلبي'
            else:
                label = 'محايد'
            
            return {
                'polarity': polarity,
                'subjectivity': subjectivity,
                'compound': compound_score,
                'keyword_sentiment': keyword_sentiment,
                'label': label,
                'confidence': abs(compound_score)
            }
        
        except Exception as e:
            self.logger.error(f"خطأ في تحليل المشاعر: {e}")
            return {'polarity': 0, 'subjectivity': 0, 'compound': 0, 'label': 'غير محدد'}
    
    def _analyze_keyword_sentiment(self, text: str) -> float:
        """تحليل المشاعر باستخدام الكلمات المفتاحية"""
        text_lower = text.lower()
        
        positive_count = sum(1 for word in self.sentiment_keywords['positive'] if word in text_lower)
        negative_count = sum(1 for word in self.sentiment_keywords['negative'] if word in text_lower)
        
        total_words = len(text.split())
        if total_words == 0:
            return 0
        
        # حساب النتيجة
        positive_score = positive_count / total_words
        negative_score = negative_count / total_words
        
        return positive_score - negative_score
    
    async def identify_affected_coins(self, news_item: Dict) -> List[Dict]:
        """تحديد العملات المتأثرة بالخبر"""
        try:
            text = (news_item.get('title', '') + ' ' + news_item.get('content', '')).lower()
            affected_coins = []
            
            for coin_id, keywords in self.crypto_keywords.items():
                relevance_score = 0
                mentioned_keywords = []
                
                for keyword in keywords:
                    if keyword.lower() in text:
                        relevance_score += 1
                        mentioned_keywords.append(keyword)
                
                if relevance_score > 0:
                    # حساب درجة التأثير
                    impact_score = self._calculate_impact_score(
                        news_item, coin_id, relevance_score, mentioned_keywords
                    )
                    
                    affected_coins.append({
                        'coin_id': coin_id,
                        'relevance_score': relevance_score,
                        'impact_score': impact_score,
                        'mentioned_keywords': mentioned_keywords
                    })
            
            # ترتيب حسب درجة التأثير
            affected_coins.sort(key=lambda x: x['impact_score'], reverse=True)
            
            return affected_coins
        
        except Exception as e:
            self.logger.error(f"خطأ في تحديد العملات المتأثرة: {e}")
            return []
    
    def _calculate_impact_score(self, news_item: Dict, coin_id: str, 
                               relevance_score: int, keywords: List[str]) -> float:
        """حساب درجة تأثير الخبر على العملة"""
        try:
            # العوامل المؤثرة على درجة التأثير
            base_score = relevance_score
            
            # أهمية المصدر
            source_weight = news_item.get('importance', 0.5)
            
            # حداثة الخبر
            time_factor = 1.0
            if 'published_date' in news_item:
                hours_ago = (datetime.now() - news_item['published_date']).total_seconds() / 3600
                time_factor = max(0.1, 1.0 - (hours_ago / 24))  # تقل الأهمية مع الوقت
            
            # طول المحتوى (الأخبار الأطول قد تكون أكثر تفصيلاً)
            content_length = len(news_item.get('content', ''))
            length_factor = min(1.0, content_length / 500)  # تطبيع حسب 500 حرف
            
            # تكرار الكلمات المفتاحية
            text = (news_item.get('title', '') + ' ' + news_item.get('content', '')).lower()
            keyword_frequency = sum(text.count(keyword.lower()) for keyword in keywords)
            frequency_factor = min(2.0, 1.0 + (keyword_frequency - 1) * 0.2)
            
            # الحساب النهائي
            impact_score = (base_score * source_weight * time_factor * 
                          length_factor * frequency_factor)
            
            return round(impact_score, 3)
        
        except Exception as e:
            self.logger.error(f"خطأ في حساب درجة التأثير: {e}")
            return 0.0
    
    async def get_market_sentiment_summary(self, hours_back: int = 24) -> Dict:
        """الحصول على ملخص مشاعر السوق"""
        try:
            # جلب الأخبار الحديثة
            news_items = await self.fetch_crypto_news(hours_back)
            
            if not news_items:
                return {'overall_sentiment': 'محايد', 'confidence': 0}
            
            # تحليل مشاعر جميع الأخبار
            sentiment_scores = []
            coin_sentiments = {}
            
            for news_item in news_items:
                sentiment = await self.analyze_sentiment(
                    news_item.get('title', '') + ' ' + news_item.get('content', '')
                )
                
                # وزن النتيجة حسب أهمية المصدر
                weighted_score = sentiment['compound'] * news_item.get('importance', 0.5)
                sentiment_scores.append(weighted_score)
                
                # تحليل مشاعر العملات المحددة
                affected_coins = await self.identify_affected_coins(news_item)
                for coin_info in affected_coins:
                    coin_id = coin_info['coin_id']
                    if coin_id not in coin_sentiments:
                        coin_sentiments[coin_id] = []
                    
                    coin_sentiments[coin_id].append({
                        'sentiment': sentiment['compound'],
                        'impact': coin_info['impact_score'],
                        'news_title': news_item.get('title', '')
                    })
            
            # حساب المشاعر الإجمالية
            if sentiment_scores:
                overall_sentiment_score = sum(sentiment_scores) / len(sentiment_scores)
                
                if overall_sentiment_score >= 0.1:
                    overall_sentiment = 'إيجابي'
                elif overall_sentiment_score <= -0.1:
                    overall_sentiment = 'سلبي'
                else:
                    overall_sentiment = 'محايد'
            else:
                overall_sentiment_score = 0
                overall_sentiment = 'محايد'
            
            # حساب مشاعر العملات الفردية
            coin_sentiment_summary = {}
            for coin_id, sentiments in coin_sentiments.items():
                if sentiments:
                    # حساب المتوسط المرجح
                    weighted_sum = sum(s['sentiment'] * s['impact'] for s in sentiments)
                    total_weight = sum(s['impact'] for s in sentiments)
                    
                    if total_weight > 0:
                        avg_sentiment = weighted_sum / total_weight
                        coin_sentiment_summary[coin_id] = {
                            'sentiment_score': avg_sentiment,
                            'sentiment_label': self._get_sentiment_label(avg_sentiment),
                            'news_count': len(sentiments),
                            'total_impact': total_weight
                        }
            
            return {
                'overall_sentiment': overall_sentiment,
                'overall_score': overall_sentiment_score,
                'confidence': abs(overall_sentiment_score),
                'news_analyzed': len(news_items),
                'coin_sentiments': coin_sentiment_summary,
                'analysis_time': datetime.now(),
                'time_period_hours': hours_back
            }
        
        except Exception as e:
            self.logger.error(f"خطأ في تحليل مشاعر السوق: {e}")
            return {'overall_sentiment': 'غير محدد', 'confidence': 0}
    
    def _get_sentiment_label(self, score: float) -> str:
        """تحويل نتيجة المشاعر إلى تصنيف"""
        if score >= 0.3:
            return 'إيجابي جداً'
        elif score >= 0.1:
            return 'إيجابي'
        elif score <= -0.3:
            return 'سلبي جداً'
        elif score <= -0.1:
            return 'سلبي'
        else:
            return 'محايد'
    
    async def get_trending_topics(self, hours_back: int = 24) -> List[Dict]:
        """الحصول على المواضيع الرائجة"""
        try:
            news_items = await self.fetch_crypto_news(hours_back)
            
            # استخراج الكلمات المفتاحية
            word_frequency = {}
            topic_news = {}
            
            for news_item in news_items:
                text = (news_item.get('title', '') + ' ' + news_item.get('content', '')).lower()
                words = re.findall(r'\b[a-zA-Z]{3,}\b', text)
                
                for word in words:
                    if word not in ['the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy', 'did', 'man', 'men', 'put', 'say', 'she', 'too', 'use']:
                        word_frequency[word] = word_frequency.get(word, 0) + 1
                        
                        if word not in topic_news:
                            topic_news[word] = []
                        topic_news[word].append(news_item)
            
            # ترتيب المواضيع حسب التكرار
            trending_topics = []
            for word, frequency in sorted(word_frequency.items(), key=lambda x: x[1], reverse=True)[:20]:
                if frequency >= 3:  # على الأقل 3 مرات
                    # حساب المشاعر للموضوع
                    topic_sentiments = []
                    for news_item in topic_news[word]:
                        sentiment = await self.analyze_sentiment(
                            news_item.get('title', '') + ' ' + news_item.get('content', '')
                        )
                        topic_sentiments.append(sentiment['compound'])
                    
                    avg_sentiment = sum(topic_sentiments) / len(topic_sentiments) if topic_sentiments else 0
                    
                    trending_topics.append({
                        'topic': word,
                        'frequency': frequency,
                        'sentiment': avg_sentiment,
                        'sentiment_label': self._get_sentiment_label(avg_sentiment),
                        'related_news_count': len(topic_news[word])
                    })
            
            return trending_topics
        
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على المواضيع الرائجة: {e}")
            return []
    
    def save_news_analysis(self, analysis_data: Dict, filename: str = None):
        """حفظ تحليل الأخبار"""
        try:
            if not filename:
                filename = f"news_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            filepath = f"data/news/{filename}"
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(analysis_data, f, ensure_ascii=False, indent=2, default=str)
            
            self.logger.info(f"تم حفظ تحليل الأخبار في {filepath}")
        
        except Exception as e:
            self.logger.error(f"خطأ في حفظ تحليل الأخبار: {e}")
    
    async def close(self):
        """إغلاق الجلسة"""
        if self.session:
            await self.session.close()