"""
🚀 محلل العملات الرقمية المتقدم - نسخة احترافية
Advanced Cryptocurrency Analyzer - Professional Edition
"""

import streamlit as st
import pandas as pd
import numpy as np
import requests
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import time
from datetime import datetime, timedelta
import sqlite3
import json
import yfinance as yf
from textblob import TextBlob
import feedparser
from bs4 import BeautifulSoup
import threading
import asyncio
import aiohttp
from concurrent.futures import ThreadPoolExecutor
import warnings
warnings.filterwarnings('ignore')

# إعداد الصفحة
st.set_page_config(
    page_title="🚀 محلل العملات الرقمية المتقدم",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="expanded"
)

# CSS متقدم للتصميم
st.markdown("""
<style>
    .main-header {
        text-align: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        padding: 2rem;
        border-radius: 15px;
        color: white;
        margin-bottom: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    }
    .metric-card {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        padding: 1.5rem;
        border-radius: 15px;
        border-left: 5px solid #667eea;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        margin: 1rem 0;
    }
    .positive { color: #00ff88; font-weight: bold; }
    .negative { color: #ff4757; font-weight: bold; }
    .neutral { color: #ffa502; font-weight: bold; }
    .news-card {
        background: #ffffff;
        padding: 1rem;
        border-radius: 10px;
        border-left: 4px solid #3742fa;
        margin: 0.5rem 0;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .alert-card {
        background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
        padding: 1rem;
        border-radius: 10px;
        margin: 1rem 0;
        color: #2c2c2c;
    }
    .success-card {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        padding: 1rem;
        border-radius: 10px;
        margin: 1rem 0;
        color: #2c2c2c;
    }
</style>
""", unsafe_allow_html=True)

class AdvancedCryptoAnalyzer:
    """محلل العملات الرقمية المتقدم"""
    
    def __init__(self):
        self.coingecko_url = "https://api.coingecko.com/api/v3"
        self.fear_greed_url = "https://api.alternative.me/fng/"
        self.news_sources = [
            "https://cointelegraph.com/rss",
            "https://decrypt.co/feed",
            "https://bitcoinmagazine.com/.rss/full/"
        ]
        
        # قائمة شاملة للعملات
        self.coins = {
            'bitcoin': {'name': 'Bitcoin', 'symbol': 'BTC', 'yahoo': 'BTC-USD'},
            'ethereum': {'name': 'Ethereum', 'symbol': 'ETH', 'yahoo': 'ETH-USD'},
            'binancecoin': {'name': 'Binance Coin', 'symbol': 'BNB', 'yahoo': 'BNB-USD'},
            'cardano': {'name': 'Cardano', 'symbol': 'ADA', 'yahoo': 'ADA-USD'},
            'solana': {'name': 'Solana', 'symbol': 'SOL', 'yahoo': 'SOL-USD'},
            'polkadot': {'name': 'Polkadot', 'symbol': 'DOT', 'yahoo': 'DOT-USD'},
            'dogecoin': {'name': 'Dogecoin', 'symbol': 'DOGE', 'yahoo': 'DOGE-USD'},
            'avalanche-2': {'name': 'Avalanche', 'symbol': 'AVAX', 'yahoo': 'AVAX-USD'},
            'chainlink': {'name': 'Chainlink', 'symbol': 'LINK', 'yahoo': 'LINK-USD'},
            'polygon': {'name': 'Polygon', 'symbol': 'MATIC', 'yahoo': 'MATIC-USD'},
            'litecoin': {'name': 'Litecoin', 'symbol': 'LTC', 'yahoo': 'LTC-USD'},
            'uniswap': {'name': 'Uniswap', 'symbol': 'UNI', 'yahoo': 'UNI-USD'},
            'cosmos': {'name': 'Cosmos', 'symbol': 'ATOM', 'yahoo': 'ATOM-USD'},
            'algorand': {'name': 'Algorand', 'symbol': 'ALGO', 'yahoo': 'ALGO-USD'},
            'stellar': {'name': 'Stellar', 'symbol': 'XLM', 'yahoo': 'XLM-USD'}
        }
        
        self.init_database()
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def init_database(self):
        """إنشاء قاعدة بيانات متقدمة"""
        try:
            conn = sqlite3.connect('advanced_crypto_data.db')
            cursor = conn.cursor()
            
            # جدول الأسعار التاريخية
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS price_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    coin_id TEXT,
                    price REAL,
                    market_cap REAL,
                    volume REAL,
                    high_24h REAL,
                    low_24h REAL,
                    change_24h REAL,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # جدول الأخبار
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS news (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT,
                    summary TEXT,
                    url TEXT,
                    sentiment REAL,
                    source TEXT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # جدول مؤشر الخوف والطمع
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS fear_greed (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    value INTEGER,
                    classification TEXT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # جدول التنبيهات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS alerts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    coin_id TEXT,
                    alert_type TEXT,
                    message TEXT,
                    price REAL,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
        except Exception as e:
            st.error(f"خطأ في إنشاء قاعدة البيانات: {e}")
    
    @st.cache_data(ttl=300)
    def get_real_market_data(_self):
        """جلب بيانات السوق الحقيقية من مصادر متعددة"""
        market_data = {}
        
        try:
            # جلب من CoinGecko
            coins_list = ','.join(_self.coins.keys())
            url = f"{_self.coingecko_url}/simple/price"
            params = {
                'ids': coins_list,
                'vs_currencies': 'usd',
                'include_market_cap': 'true',
                'include_24hr_vol': 'true',
                'include_24hr_change': 'true',
                'include_24hr_high_low': 'true'
            }
            
            response = _self.session.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                coingecko_data = response.json()
                
                # دمج مع بيانات Yahoo Finance للتأكد
                for coin_id, coin_info in _self.coins.items():
                    try:
                        # بيانات CoinGecko
                        cg_data = coingecko_data.get(coin_id, {})
                        
                        # بيانات Yahoo Finance
                        ticker = yf.Ticker(coin_info['yahoo'])
                        yf_data = ticker.history(period="1d", interval="1d")
                        
                        if not yf_data.empty:
                            latest_yf = yf_data.iloc[-1]
                            
                            # دمج البيانات
                            market_data[coin_id] = {
                                'usd': cg_data.get('usd', latest_yf['Close']),
                                'usd_market_cap': cg_data.get('usd_market_cap', 0),
                                'usd_24h_vol': cg_data.get('usd_24h_vol', latest_yf['Volume'] * latest_yf['Close']),
                                'usd_24h_change': cg_data.get('usd_24h_change', 0),
                                'usd_24h_high': cg_data.get('usd_24h_high', latest_yf['High']),
                                'usd_24h_low': cg_data.get('usd_24h_low', latest_yf['Low']),
                                'source': 'CoinGecko + Yahoo Finance'
                            }
                        else:
                            # CoinGecko فقط
                            market_data[coin_id] = {
                                'usd': cg_data.get('usd', 0),
                                'usd_market_cap': cg_data.get('usd_market_cap', 0),
                                'usd_24h_vol': cg_data.get('usd_24h_vol', 0),
                                'usd_24h_change': cg_data.get('usd_24h_change', 0),
                                'usd_24h_high': cg_data.get('usd_24h_high', 0),
                                'usd_24h_low': cg_data.get('usd_24h_low', 0),
                                'source': 'CoinGecko'
                            }
                    except Exception as e:
                        st.warning(f"خطأ في جلب بيانات {coin_id}: {e}")
                
                # حفظ في قاعدة البيانات
                _self.save_market_data(market_data)
                
                return market_data
            
            else:
                st.error(f"خطأ في CoinGecko API: {response.status_code}")
                return _self.get_cached_market_data()
        
        except Exception as e:
            st.error(f"خطأ في جلب البيانات: {e}")
            return _self.get_cached_market_data()
    
    def save_market_data(self, market_data):
        """حفظ بيانات السوق في قاعدة البيانات"""
        try:
            conn = sqlite3.connect('advanced_crypto_data.db')
            cursor = conn.cursor()
            
            for coin_id, data in market_data.items():
                cursor.execute('''
                    INSERT INTO price_history 
                    (coin_id, price, market_cap, volume, high_24h, low_24h, change_24h)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    coin_id,
                    data.get('usd', 0),
                    data.get('usd_market_cap', 0),
                    data.get('usd_24h_vol', 0),
                    data.get('usd_24h_high', 0),
                    data.get('usd_24h_low', 0),
                    data.get('usd_24h_change', 0)
                ))
            
            conn.commit()
            conn.close()
        except Exception as e:
            st.error(f"خطأ في حفظ البيانات: {e}")
    
    def get_cached_market_data(self):
        """جلب البيانات المحفوظة"""
        try:
            conn = sqlite3.connect('advanced_crypto_data.db')
            
            query = '''
                SELECT coin_id, price, market_cap, volume, high_24h, low_24h, change_24h, timestamp
                FROM price_history
                WHERE timestamp IN (
                    SELECT MAX(timestamp)
                    FROM price_history
                    GROUP BY coin_id
                )
            '''
            
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            if not df.empty:
                cached_data = {}
                for _, row in df.iterrows():
                    cached_data[row['coin_id']] = {
                        'usd': row['price'],
                        'usd_market_cap': row['market_cap'],
                        'usd_24h_vol': row['volume'],
                        'usd_24h_high': row['high_24h'],
                        'usd_24h_low': row['low_24h'],
                        'usd_24h_change': row['change_24h'],
                        'source': 'Cached Data'
                    }
                return cached_data
            
            return {}
        
        except Exception as e:
            st.error(f"خطأ في جلب البيانات المحفوظة: {e}")
            return {}
    
    def get_real_historical_data(self, coin_id, days=30):
        """جلب البيانات التاريخية الحقيقية"""
        try:
            # جلب من Yahoo Finance
            coin_info = self.coins.get(coin_id, {})
            yahoo_symbol = coin_info.get('yahoo', '')
            
            if yahoo_symbol:
                ticker = yf.Ticker(yahoo_symbol)
                
                # تحديد الفترة
                if days <= 7:
                    period = "7d"
                    interval = "1h"
                elif days <= 30:
                    period = "1mo"
                    interval = "1d"
                elif days <= 90:
                    period = "3mo"
                    interval = "1d"
                else:
                    period = "1y"
                    interval = "1wk"
                
                hist_data = ticker.history(period=period, interval=interval)
                
                if not hist_data.empty:
                    # تحويل إلى DataFrame مناسب
                    df = pd.DataFrame({
                        'timestamp': hist_data.index,
                        'price': hist_data['Close'],
                        'high': hist_data['High'],
                        'low': hist_data['Low'],
                        'volume': hist_data['Volume'],
                        'open': hist_data['Open']
                    })
                    
                    df['timestamp'] = pd.to_datetime(df['timestamp'])
                    df = df.reset_index(drop=True)
                    
                    return df
            
            # إذا فشل Yahoo Finance، جرب CoinGecko
            return self.get_coingecko_historical(coin_id, days)
        
        except Exception as e:
            st.warning(f"خطأ في جلب البيانات التاريخية من Yahoo Finance: {e}")
            return self.get_coingecko_historical(coin_id, days)
    
    def get_coingecko_historical(self, coin_id, days):
        """جلب البيانات التاريخية من CoinGecko"""
        try:
            url = f"{self.coingecko_url}/coins/{coin_id}/market_chart"
            params = {
                'vs_currency': 'usd',
                'days': days,
                'interval': 'daily' if days > 30 else 'hourly'
            }
            
            response = self.session.get(url, params=params, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                
                prices = data.get('prices', [])
                volumes = data.get('total_volumes', [])
                
                if prices:
                    df = pd.DataFrame({
                        'timestamp': [datetime.fromtimestamp(p[0]/1000) for p in prices],
                        'price': [p[1] for p in prices],
                        'volume': [v[1] if v else 0 for v in volumes[:len(prices)]]
                    })
                    
                    # إضافة high, low, open (تقديرات)
                    df['high'] = df['price'] * 1.02
                    df['low'] = df['price'] * 0.98
                    df['open'] = df['price'].shift(1).fillna(df['price'])
                    
                    return df
            
            return pd.DataFrame()
        
        except Exception as e:
            st.error(f"خطأ في جلب البيانات من CoinGecko: {e}")
            return pd.DataFrame()
    
    def calculate_advanced_indicators(self, df):
        """حساب المؤشرات الفنية المتقدمة"""
        if df.empty or len(df) < 20:
            return {}
        
        prices = df['price'].values
        highs = df.get('high', df['price']).values
        lows = df.get('low', df['price']).values
        volumes = df.get('volume', pd.Series([1]*len(df))).values
        
        indicators = {}
        
        try:
            # RSI
            def calculate_rsi(prices, period=14):
                deltas = np.diff(prices)
                gains = np.where(deltas > 0, deltas, 0)
                losses = np.where(deltas < 0, -deltas, 0)
                
                avg_gains = []
                avg_losses = []
                
                for i in range(period, len(gains)):
                    avg_gain = np.mean(gains[i-period:i])
                    avg_loss = np.mean(losses[i-period:i])
                    avg_gains.append(avg_gain)
                    avg_losses.append(avg_loss)
                
                if avg_losses[-1] == 0:
                    return 100
                
                rs = avg_gains[-1] / avg_losses[-1]
                rsi = 100 - (100 / (1 + rs))
                return rsi
            
            # MACD
            def calculate_macd(prices):
                ema_12 = pd.Series(prices).ewm(span=12).mean()
                ema_26 = pd.Series(prices).ewm(span=26).mean()
                macd_line = ema_12 - ema_26
                signal_line = macd_line.ewm(span=9).mean()
                histogram = macd_line - signal_line
                
                return {
                    'macd': macd_line.iloc[-1],
                    'signal': signal_line.iloc[-1],
                    'histogram': histogram.iloc[-1]
                }
            
            # Bollinger Bands
            def calculate_bollinger(prices, period=20):
                sma = np.mean(prices[-period:])
                std = np.std(prices[-period:])
                upper = sma + (2 * std)
                lower = sma - (2 * std)
                
                return {
                    'upper': upper,
                    'middle': sma,
                    'lower': lower,
                    'width': (upper - lower) / sma * 100
                }
            
            # Stochastic
            def calculate_stochastic(highs, lows, closes, k_period=14):
                lowest_low = np.min(lows[-k_period:])
                highest_high = np.max(highs[-k_period:])
                
                if highest_high == lowest_low:
                    return 50
                
                k_percent = ((closes[-1] - lowest_low) / (highest_high - lowest_low)) * 100
                return k_percent
            
            # Williams %R
            def calculate_williams_r(highs, lows, closes, period=14):
                highest_high = np.max(highs[-period:])
                lowest_low = np.min(lows[-period:])
                
                if highest_high == lowest_low:
                    return -50
                
                williams_r = ((highest_high - closes[-1]) / (highest_high - lowest_low)) * -100
                return williams_r
            
            # حساب المؤشرات
            indicators['rsi'] = calculate_rsi(prices)
            indicators['macd'] = calculate_macd(prices)
            indicators['bollinger'] = calculate_bollinger(prices)
            indicators['stochastic'] = calculate_stochastic(highs, lows, prices)
            indicators['williams_r'] = calculate_williams_r(highs, lows, prices)
            
            # المتوسطات المتحركة
            indicators['sma_20'] = np.mean(prices[-20:])
            indicators['sma_50'] = np.mean(prices[-50:]) if len(prices) >= 50 else np.mean(prices)
            indicators['ema_12'] = pd.Series(prices).ewm(span=12).mean().iloc[-1]
            indicators['ema_26'] = pd.Series(prices).ewm(span=26).mean().iloc[-1]
            
            # مؤشر القوة النسبية للحجم
            if len(volumes) > 1:
                volume_rsi = calculate_rsi(volumes)
                indicators['volume_rsi'] = volume_rsi
            
            # تحليل الاتجاه
            current_price = prices[-1]
            sma_20 = indicators['sma_20']
            sma_50 = indicators['sma_50']
            
            # تحديد الاتجاه
            if current_price > sma_20 > sma_50:
                trend = "صاعد قوي"
                trend_color = "positive"
            elif current_price > sma_20:
                trend = "صاعد"
                trend_color = "positive"
            elif current_price < sma_20 < sma_50:
                trend = "هابط قوي"
                trend_color = "negative"
            elif current_price < sma_20:
                trend = "هابط"
                trend_color = "negative"
            else:
                trend = "جانبي"
                trend_color = "neutral"
            
            indicators['trend'] = trend
            indicators['trend_color'] = trend_color
            
            # إشارات التداول المتقدمة
            signals = []
            signal_strength = 0
            
            # إشارات RSI
            if indicators['rsi'] < 30:
                signals.append("RSI: ذروة بيع - فرصة شراء")
                signal_strength += 2
            elif indicators['rsi'] > 70:
                signals.append("RSI: ذروة شراء - فرصة بيع")
                signal_strength -= 2
            
            # إشارات MACD
            macd_data = indicators['macd']
            if macd_data['macd'] > macd_data['signal'] and macd_data['histogram'] > 0:
                signals.append("MACD: إشارة شراء")
                signal_strength += 1
            elif macd_data['macd'] < macd_data['signal'] and macd_data['histogram'] < 0:
                signals.append("MACD: إشارة بيع")
                signal_strength -= 1
            
            # إشارات Bollinger Bands
            bb_data = indicators['bollinger']
            if current_price <= bb_data['lower']:
                signals.append("Bollinger: قرب الحد السفلي - فرصة شراء")
                signal_strength += 1
            elif current_price >= bb_data['upper']:
                signals.append("Bollinger: قرب الحد العلوي - فرصة بيع")
                signal_strength -= 1
            
            # إشارات Stochastic
            if indicators['stochastic'] < 20:
                signals.append("Stochastic: ذروة بيع")
                signal_strength += 1
            elif indicators['stochastic'] > 80:
                signals.append("Stochastic: ذروة شراء")
                signal_strength -= 1
            
            # التوصية النهائية
            if signal_strength >= 3:
                recommendation = "شراء قوي"
                rec_color = "positive"
            elif signal_strength >= 1:
                recommendation = "شراء"
                rec_color = "positive"
            elif signal_strength <= -3:
                recommendation = "بيع قوي"
                rec_color = "negative"
            elif signal_strength <= -1:
                recommendation = "بيع"
                rec_color = "negative"
            else:
                recommendation = "انتظار"
                rec_color = "neutral"
            
            indicators['signals'] = signals
            indicators['recommendation'] = recommendation
            indicators['rec_color'] = rec_color
            indicators['signal_strength'] = signal_strength
            indicators['current_price'] = current_price
            
            return indicators
        
        except Exception as e:
            st.error(f"خطأ في حساب المؤشرات: {e}")
            return {}
    
    @st.cache_data(ttl=1800)  # 30 دقيقة
    def get_fear_greed_index(_self):
        """جلب مؤشر الخوف والطمع الحقيقي"""
        try:
            response = _self.session.get(_self.fear_greed_url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'data' in data and len(data['data']) > 0:
                    latest = data['data'][0]
                    value = int(latest['value'])
                    classification = latest['value_classification']
                    
                    # حفظ في قاعدة البيانات
                    _self.save_fear_greed(value, classification)
                    
                    return {
                        'value': value,
                        'classification': classification,
                        'timestamp': latest['timestamp']
                    }
            
            return _self.get_cached_fear_greed()
        
        except Exception as e:
            st.warning(f"خطأ في جلب مؤشر الخوف والطمع: {e}")
            return _self.get_cached_fear_greed()
    
    def save_fear_greed(self, value, classification):
        """حفظ مؤشر الخوف والطمع"""
        try:
            conn = sqlite3.connect('advanced_crypto_data.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO fear_greed (value, classification)
                VALUES (?, ?)
            ''', (value, classification))
            
            conn.commit()
            conn.close()
        except Exception as e:
            st.error(f"خطأ في حفظ مؤشر الخوف والطمع: {e}")
    
    def get_cached_fear_greed(self):
        """جلب مؤشر الخوف والطمع المحفوظ"""
        try:
            conn = sqlite3.connect('advanced_crypto_data.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT value, classification, timestamp
                FROM fear_greed
                ORDER BY timestamp DESC
                LIMIT 1
            ''')
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                return {
                    'value': result[0],
                    'classification': result[1],
                    'timestamp': result[2]
                }
            
            return {'value': 50, 'classification': 'Neutral', 'timestamp': 'Unknown'}
        
        except Exception as e:
            return {'value': 50, 'classification': 'Neutral', 'timestamp': 'Unknown'}
    
    @st.cache_data(ttl=3600)  # ساعة واحدة
    def get_crypto_news(_self):
        """جلب الأخبار الحقيقية وتحليل المشاعر"""
        news_list = []
        
        for source_url in _self.news_sources:
            try:
                feed = feedparser.parse(source_url)
                
                for entry in feed.entries[:5]:  # أول 5 أخبار من كل مصدر
                    title = entry.title
                    summary = entry.get('summary', '')[:200] + '...'
                    link = entry.link
                    
                    # تحليل المشاعر
                    sentiment_text = f"{title} {summary}"
                    blob = TextBlob(sentiment_text)
                    sentiment_score = blob.sentiment.polarity
                    
                    # تصنيف المشاعر
                    if sentiment_score > 0.1:
                        sentiment = "إيجابي"
                        sentiment_color = "positive"
                    elif sentiment_score < -0.1:
                        sentiment = "سلبي"
                        sentiment_color = "negative"
                    else:
                        sentiment = "محايد"
                        sentiment_color = "neutral"
                    
                    news_item = {
                        'title': title,
                        'summary': summary,
                        'url': link,
                        'sentiment': sentiment,
                        'sentiment_color': sentiment_color,
                        'sentiment_score': sentiment_score,
                        'source': source_url.split('/')[2]
                    }
                    
                    news_list.append(news_item)
                    
                    # حفظ في قاعدة البيانات
                    _self.save_news(news_item)
            
            except Exception as e:
                st.warning(f"خطأ في جلب الأخبار من {source_url}: {e}")
        
        return sorted(news_list, key=lambda x: x['sentiment_score'], reverse=True)
    
    def save_news(self, news_item):
        """حفظ الأخبار في قاعدة البيانات"""
        try:
            conn = sqlite3.connect('advanced_crypto_data.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR IGNORE INTO news (title, summary, url, sentiment, source)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                news_item['title'],
                news_item['summary'],
                news_item['url'],
                news_item['sentiment_score'],
                news_item['source']
            ))
            
            conn.commit()
            conn.close()
        except Exception as e:
            pass  # تجاهل الأخطاء في حفظ الأخبار
    
    def create_advanced_chart(self, df, coin_name, indicators):
        """إنشاء شارت متقدم مع المؤشرات"""
        if df.empty:
            return None
        
        # إنشاء subplots
        fig = make_subplots(
            rows=4, cols=1,
            shared_xaxes=True,
            vertical_spacing=0.05,
            subplot_titles=(
                f'سعر {coin_name} مع Bollinger Bands',
                'RSI',
                'MACD',
                'الحجم'
            ),
            row_heights=[0.5, 0.2, 0.2, 0.1]
        )
        
        # شارت السعر الرئيسي
        fig.add_trace(
            go.Scatter(
                x=df['timestamp'],
                y=df['price'],
                mode='lines',
                name='السعر',
                line=dict(color='#00ff88', width=2)
            ),
            row=1, col=1
        )
        
        # Bollinger Bands
        if 'bollinger' in indicators:
            bb = indicators['bollinger']
            
            # الحد العلوي
            fig.add_trace(
                go.Scatter(
                    x=df['timestamp'],
                    y=[bb['upper']] * len(df),
                    mode='lines',
                    name='Bollinger Upper',
                    line=dict(color='red', dash='dash'),
                    opacity=0.7
                ),
                row=1, col=1
            )
            
            # الحد السفلي
            fig.add_trace(
                go.Scatter(
                    x=df['timestamp'],
                    y=[bb['lower']] * len(df),
                    mode='lines',
                    name='Bollinger Lower',
                    line=dict(color='red', dash='dash'),
                    opacity=0.7,
                    fill='tonexty',
                    fillcolor='rgba(255,0,0,0.1)'
                ),
                row=1, col=1
            )
            
            # المتوسط المتحرك
            fig.add_trace(
                go.Scatter(
                    x=df['timestamp'],
                    y=[bb['middle']] * len(df),
                    mode='lines',
                    name='SMA 20',
                    line=dict(color='orange', width=1)
                ),
                row=1, col=1
            )
        
        # RSI
        if 'rsi' in indicators:
            rsi_values = [indicators['rsi']] * len(df)
            
            fig.add_trace(
                go.Scatter(
                    x=df['timestamp'],
                    y=rsi_values,
                    mode='lines',
                    name='RSI',
                    line=dict(color='purple', width=2)
                ),
                row=2, col=1
            )
            
            # خطوط RSI المرجعية
            fig.add_hline(y=70, line_dash="dash", line_color="red", row=2, col=1)
            fig.add_hline(y=30, line_dash="dash", line_color="green", row=2, col=1)
            fig.add_hline(y=50, line_dash="dot", line_color="gray", row=2, col=1)
        
        # MACD
        if 'macd' in indicators:
            macd_data = indicators['macd']
            
            fig.add_trace(
                go.Scatter(
                    x=df['timestamp'],
                    y=[macd_data['macd']] * len(df),
                    mode='lines',
                    name='MACD',
                    line=dict(color='blue', width=2)
                ),
                row=3, col=1
            )
            
            fig.add_trace(
                go.Scatter(
                    x=df['timestamp'],
                    y=[macd_data['signal']] * len(df),
                    mode='lines',
                    name='Signal',
                    line=dict(color='red', width=1)
                ),
                row=3, col=1
            )
            
            # Histogram
            fig.add_trace(
                go.Bar(
                    x=df['timestamp'],
                    y=[macd_data['histogram']] * len(df),
                    name='Histogram',
                    marker_color='gray',
                    opacity=0.6
                ),
                row=3, col=1
            )
        
        # الحجم
        colors = ['green' if df['price'].iloc[i] >= df['price'].iloc[i-1] 
                 else 'red' for i in range(1, len(df))]
        colors.insert(0, 'green')
        
        fig.add_trace(
            go.Bar(
                x=df['timestamp'],
                y=df.get('volume', [1]*len(df)),
                name='الحجم',
                marker_color=colors,
                opacity=0.7
            ),
            row=4, col=1
        )
        
        # تحديث التخطيط
        fig.update_layout(
            title=f'تحليل متقدم لـ {coin_name}',
            template='plotly_dark',
            height=800,
            showlegend=True,
            xaxis_rangeslider_visible=False
        )
        
        # تحديث محاور Y
        fig.update_yaxes(title_text="السعر ($)", row=1, col=1)
        fig.update_yaxes(title_text="RSI", row=2, col=1, range=[0, 100])
        fig.update_yaxes(title_text="MACD", row=3, col=1)
        fig.update_yaxes(title_text="الحجم", row=4, col=1)
        
        return fig
    
    def generate_alerts(self, coin_id, current_data, indicators):
        """إنشاء تنبيهات ذكية"""
        alerts = []
        
        try:
            current_price = current_data.get('usd', 0)
            change_24h = current_data.get('usd_24h_change', 0)
            
            # تنبيهات الأسعار
            if abs(change_24h) > 10:
                alert_type = "تحرك سعري كبير"
                message = f"تغيير {change_24h:+.2f}% في 24 ساعة"
                alerts.append({
                    'type': alert_type,
                    'message': message,
                    'severity': 'high' if abs(change_24h) > 20 else 'medium',
                    'price': current_price
                })
            
            # تنبيهات المؤشرات الفنية
            if 'rsi' in indicators:
                rsi = indicators['rsi']
                if rsi < 25:
                    alerts.append({
                        'type': 'مؤشر فني',
                        'message': f'RSI في ذروة بيع شديدة ({rsi:.1f})',
                        'severity': 'high',
                        'price': current_price
                    })
                elif rsi > 75:
                    alerts.append({
                        'type': 'مؤشر فني',
                        'message': f'RSI في ذروة شراء شديدة ({rsi:.1f})',
                        'severity': 'high',
                        'price': current_price
                    })
            
            # تنبيهات الحجم
            volume = current_data.get('usd_24h_vol', 0)
            if 'volume_rsi' in indicators and indicators['volume_rsi'] > 80:
                alerts.append({
                    'type': 'حجم تداول',
                    'message': 'حجم تداول مرتفع جداً - نشاط غير عادي',
                    'severity': 'medium',
                    'price': current_price
                })
            
            # حفظ التنبيهات
            for alert in alerts:
                self.save_alert(coin_id, alert)
            
            return alerts
        
        except Exception as e:
            st.error(f"خطأ في إنشاء التنبيهات: {e}")
            return []
    
    def save_alert(self, coin_id, alert):
        """حفظ التنبيه في قاعدة البيانات"""
        try:
            conn = sqlite3.connect('advanced_crypto_data.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO alerts (coin_id, alert_type, message, price)
                VALUES (?, ?, ?, ?)
            ''', (
                coin_id,
                alert['type'],
                alert['message'],
                alert['price']
            ))
            
            conn.commit()
            conn.close()
        except Exception as e:
            pass  # تجاهل أخطاء حفظ التنبيهات

def main():
    """الدالة الرئيسية للتطبيق المتقدم"""
    
    # العنوان الرئيسي
    st.markdown("""
    <div class="main-header">
        <h1>🚀 محلل العملات الرقمية المتقدم</h1>
        <p>تحليل احترافي شامل مع بيانات حقيقية ومؤشرات متقدمة</p>
        <small>Professional Cryptocurrency Analysis with Real Data & Advanced Indicators</small>
    </div>
    """, unsafe_allow_html=True)
    
    # إنشاء المحلل المتقدم
    analyzer = AdvancedCryptoAnalyzer()
    
    # الشريط الجانبي المتقدم
    st.sidebar.header("⚙️ لوحة التحكم المتقدمة")
    
    # اختيار العملة
    selected_coin = st.sidebar.selectbox(
        "اختر العملة الرقمية:",
        options=list(analyzer.coins.keys()),
        format_func=lambda x: f"{analyzer.coins[x]['name']} ({analyzer.coins[x]['symbol']})",
        index=0
    )
    
    # اختيار الفترة الزمنية
    time_period = st.sidebar.selectbox(
        "الفترة الزمنية للتحليل:",
        options=[7, 30, 90, 365],
        format_func=lambda x: f"{x} يوم",
        index=1
    )
    
    # خيارات التحليل
    st.sidebar.subheader("🔍 خيارات التحليل")
    show_advanced_indicators = st.sidebar.checkbox("المؤشرات المتقدمة", value=True)
    show_news = st.sidebar.checkbox("تحليل الأخبار", value=True)
    show_fear_greed = st.sidebar.checkbox("مؤشر الخوف والطمع", value=True)
    show_alerts = st.sidebar.checkbox("التنبيهات الذكية", value=True)
    
    # زر التحديث
    if st.sidebar.button("🔄 تحديث جميع البيانات", type="primary"):
        st.cache_data.clear()
        st.rerun()
    
    # معلومات النظام
    st.sidebar.markdown("---")
    st.sidebar.info(f"""
    **📊 حالة النظام:**
    - ⏰ آخر تحديث: {datetime.now().strftime('%H:%M:%S')}
    - 🔗 مصادر البيانات: CoinGecko + Yahoo Finance
    - 📈 عدد العملات: {len(analyzer.coins)}
    - 🗃️ قاعدة البيانات: SQLite محلية
    """)
    
    # جلب البيانات
    with st.spinner("🔄 جاري جلب البيانات الحقيقية..."):
        market_data = analyzer.get_real_market_data()
        historical_data = analyzer.get_real_historical_data(selected_coin, time_period)
        
        if show_fear_greed:
            fear_greed_data = analyzer.get_fear_greed_index()
        
        if show_news:
            news_data = analyzer.get_crypto_news()
    
    if market_data and selected_coin in market_data:
        coin_data = market_data[selected_coin]
        coin_info = analyzer.coins[selected_coin]
        
        # الصف الأول: المقاييس الأساسية
        col1, col2, col3, col4, col5 = st.columns(5)
        
        current_price = coin_data.get('usd', 0)
        market_cap = coin_data.get('usd_market_cap', 0)
        volume = coin_data.get('usd_24h_vol', 0)
        change_24h = coin_data.get('usd_24h_change', 0)
        high_24h = coin_data.get('usd_24h_high', 0)
        low_24h = coin_data.get('usd_24h_low', 0)
        
        with col1:
            st.metric(
                f"💰 {coin_info['symbol']} السعر",
                f"${current_price:,.4f}",
                f"{change_24h:+.2f}%"
            )
        
        with col2:
            st.metric(
                "📊 القيمة السوقية",
                f"${market_cap/1e9:.2f}B" if market_cap > 1e9 else f"${market_cap/1e6:.2f}M"
            )
        
        with col3:
            st.metric(
                "📈 أعلى 24س",
                f"${high_24h:,.4f}"
            )
        
        with col4:
            st.metric(
                "📉 أقل 24س",
                f"${low_24h:,.4f}"
            )
        
        with col5:
            st.metric(
                "💹 الحجم 24س",
                f"${volume/1e6:.1f}M" if volume > 1e6 else f"${volume/1e3:.1f}K"
            )
        
        # مؤشر الخوف والطمع
        if show_fear_greed and 'fear_greed_data' in locals():
            st.markdown("---")
            col1, col2, col3 = st.columns([1, 2, 1])
            
            with col2:
                fg_value = fear_greed_data['value']
                fg_class = fear_greed_data['classification']
                
                # تحديد اللون
                if fg_value < 25:
                    fg_color = "#ff4757"  # أحمر
                elif fg_value < 45:
                    fg_color = "#ffa502"  # برتقالي
                elif fg_value < 55:
                    fg_color = "#fffa65"  # أصفر
                elif fg_value < 75:
                    fg_color = "#7bed9f"  # أخضر فاتح
                else:
                    fg_color = "#00ff88"  # أخضر
                
                st.markdown(f"""
                <div class="metric-card" style="text-align: center;">
                    <h3>😨 مؤشر الخوف والطمع</h3>
                    <h1 style="color: {fg_color}; font-size: 3em;">{fg_value}</h1>
                    <h2>{fg_class}</h2>
                    <p>المصدر: Alternative.me</p>
                </div>
                """, unsafe_allow_html=True)
        
        # التحليل الفني المتقدم
        if show_advanced_indicators and not historical_data.empty:
            st.markdown("---")
            st.subheader("🔍 التحليل الفني المتقدم")
            
            indicators = analyzer.calculate_advanced_indicators(historical_data)
            
            if indicators:
                # الصف الأول: التوصية والاتجاه
                col1, col2 = st.columns(2)
                
                with col1:
                    st.markdown(f"""
                    <div class="metric-card">
                        <h4>🎯 التوصية</h4>
                        <h2 class="{indicators['rec_color']}">{indicators['recommendation']}</h2>
                        <p>قوة الإشارة: {indicators['signal_strength']}/5</p>
                    </div>
                    """, unsafe_allow_html=True)
                
                with col2:
                    st.markdown(f"""
                    <div class="metric-card">
                        <h4>📈 الاتجاه العام</h4>
                        <h2 class="{indicators['trend_color']}">{indicators['trend']}</h2>
                        <p>السعر الحالي: ${indicators['current_price']:,.4f}</p>
                    </div>
                    """, unsafe_allow_html=True)
                
                # الصف الثاني: المؤشرات الفنية
                col1, col2, col3, col4 = st.columns(4)
                
                with col1:
                    rsi = indicators.get('rsi', 0)
                    rsi_color = "negative" if rsi > 70 else "positive" if rsi < 30 else "neutral"
                    st.metric("RSI", f"{rsi:.1f}", delta_color="off")
                    st.markdown(f'<p class="{rsi_color}">{"ذروة شراء" if rsi > 70 else "ذروة بيع" if rsi < 30 else "محايد"}</p>', unsafe_allow_html=True)
                
                with col2:
                    stoch = indicators.get('stochastic', 0)
                    stoch_color = "negative" if stoch > 80 else "positive" if stoch < 20 else "neutral"
                    st.metric("Stochastic", f"{stoch:.1f}", delta_color="off")
                    st.markdown(f'<p class="{stoch_color}">{"ذروة شراء" if stoch > 80 else "ذروة بيع" if stoch < 20 else "محايد"}</p>', unsafe_allow_html=True)
                
                with col3:
                    williams = indicators.get('williams_r', 0)
                    williams_color = "negative" if williams > -20 else "positive" if williams < -80 else "neutral"
                    st.metric("Williams %R", f"{williams:.1f}", delta_color="off")
                    st.markdown(f'<p class="{williams_color}">{"ذروة شراء" if williams > -20 else "ذروة بيع" if williams < -80 else "محايد"}</p>', unsafe_allow_html=True)
                
                with col4:
                    if 'bollinger' in indicators:
                        bb_width = indicators['bollinger']['width']
                        st.metric("BB Width", f"{bb_width:.2f}%", delta_color="off")
                        st.markdown(f'<p class="{"positive" if bb_width > 10 else "neutral"}">{"تقلبات عالية" if bb_width > 10 else "تقلبات منخفضة"}</p>', unsafe_allow_html=True)
                
                # المتوسطات المتحركة
                st.subheader("📊 المتوسطات المتحركة")
                col1, col2, col3, col4 = st.columns(4)
                
                with col1:
                    st.metric("SMA 20", f"${indicators.get('sma_20', 0):,.4f}")
                
                with col2:
                    st.metric("SMA 50", f"${indicators.get('sma_50', 0):,.4f}")
                
                with col3:
                    st.metric("EMA 12", f"${indicators.get('ema_12', 0):,.4f}")
                
                with col4:
                    st.metric("EMA 26", f"${indicators.get('ema_26', 0):,.4f}")
                
                # الإشارات
                if indicators.get('signals'):
                    st.subheader("🚨 الإشارات الفنية")
                    for signal in indicators['signals']:
                        st.markdown(f"• {signal}")
        
        # الشارت المتقدم
        st.markdown("---")
        st.subheader("📈 الشارت المتقدم")
        
        if not historical_data.empty:
            if show_advanced_indicators and 'indicators' in locals():
                chart = analyzer.create_advanced_chart(
                    historical_data, 
                    coin_info['name'],
                    indicators
                )
            else:
                # شارت بسيط
                fig = go.Figure()
                fig.add_trace(go.Scatter(
                    x=historical_data['timestamp'],
                    y=historical_data['price'],
                    mode='lines',
                    name='السعر',
                    line=dict(color='#00ff88', width=2)
                ))
                fig.update_layout(
                    title=f'سعر {coin_info["name"]}',
                    template='plotly_dark',
                    height=400
                )
                chart = fig
            
            if chart:
                st.plotly_chart(chart, use_container_width=True)
        else:
            st.warning("لا توجد بيانات تاريخية متاحة")
        
        # التنبيهات الذكية
        if show_alerts:
            st.markdown("---")
            st.subheader("🚨 التنبيهات الذكية")
            
            if 'indicators' in locals():
                alerts = analyzer.generate_alerts(selected_coin, coin_data, indicators)
                
                if alerts:
                    for alert in alerts:
                        severity_color = {
                            'high': 'alert-card',
                            'medium': 'success-card',
                            'low': 'metric-card'
                        }.get(alert['severity'], 'metric-card')
                        
                        st.markdown(f"""
                        <div class="{severity_color}">
                            <h4>⚠️ {alert['type']}</h4>
                            <p>{alert['message']}</p>
                            <small>السعر: ${alert['price']:,.4f}</small>
                        </div>
                        """, unsafe_allow_html=True)
                else:
                    st.success("✅ لا توجد تنبيهات حالياً - الوضع مستقر")
        
        # تحليل الأخبار
        if show_news and 'news_data' in locals():
            st.markdown("---")
            st.subheader("📰 تحليل الأخبار والمشاعر")
            
            if news_data:
                # إحصائيات المشاعر
                positive_news = len([n for n in news_data if n['sentiment'] == 'إيجابي'])
                negative_news = len([n for n in news_data if n['sentiment'] == 'سلبي'])
                neutral_news = len([n for n in news_data if n['sentiment'] == 'محايد'])
                
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    st.metric("📈 أخبار إيجابية", positive_news)
                
                with col2:
                    st.metric("📉 أخبار سلبية", negative_news)
                
                with col3:
                    st.metric("⚖️ أخبار محايدة", neutral_news)
                
                # عرض الأخبار
                for news in news_data[:10]:  # أول 10 أخبار
                    st.markdown(f"""
                    <div class="news-card">
                        <h4>{news['title']}</h4>
                        <p>{news['summary']}</p>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span class="{news['sentiment_color']}">المشاعر: {news['sentiment']}</span>
                            <small>المصدر: {news['source']}</small>
                        </div>
                        <a href="{news['url']}" target="_blank">🔗 اقرأ المزيد</a>
                    </div>
                    """, unsafe_allow_html=True)
            else:
                st.warning("لا يمكن جلب الأخبار حالياً")
        
        # جدول جميع العملات
        st.markdown("---")
        st.subheader("📊 جميع العملات الرقمية")
        
        # تحويل البيانات إلى DataFrame
        df_data = []
        for coin_id, data in market_data.items():
            coin_info = analyzer.coins.get(coin_id, {})
            df_data.append({
                'العملة': coin_info.get('name', coin_id),
                'الرمز': coin_info.get('symbol', ''),
                'السعر': f"${data.get('usd', 0):,.4f}",
                'التغيير 24س': f"{data.get('usd_24h_change', 0):+.2f}%",
                'أعلى 24س': f"${data.get('usd_24h_high', 0):,.4f}",
                'أقل 24س': f"${data.get('usd_24h_low', 0):,.4f}",
                'القيمة السوقية': f"${data.get('usd_market_cap', 0)/1e9:.2f}B" if data.get('usd_market_cap', 0) > 1e9 else f"${data.get('usd_market_cap', 0)/1e6:.2f}M",
                'الحجم': f"${data.get('usd_24h_vol', 0)/1e6:.1f}M" if data.get('usd_24h_vol', 0) > 1e6 else f"${data.get('usd_24h_vol', 0)/1e3:.1f}K",
                'المصدر': data.get('source', 'Unknown')
            })
        
        if df_data:
            df = pd.DataFrame(df_data)
            st.dataframe(df, use_container_width=True)
    
    else:
        st.error("❌ لا يمكن جلب البيانات حالياً. يرجى التحقق من الاتصال بالإنترنت والمحاولة لاحقاً.")
    
    # معلومات إضافية
    st.sidebar.markdown("---")
    st.sidebar.warning("""
    **⚠️ تحذيرات مهمة:**
    
    • هذه الأداة للأغراض التعليمية والتحليلية فقط
    • لا تستخدم أموال حقيقية بناءً على هذه التوصيات
    • استشر خبير مالي مؤهل قبل اتخاذ قرارات استثمارية
    • الأسواق المالية محفوفة بالمخاطر
    • الأداء السابق لا يضمن النتائج المستقبلية
    """)
    
    # معلومات التطبيق
    st.sidebar.markdown("---")
    st.sidebar.success("""
    **🚀 محلل العملات الرقمية المتقدم v3.0**
    
    **الميزات:**
    ✅ بيانات حقيقية 100%
    ✅ 15 عملة رقمية
    ✅ مؤشرات فنية متقدمة
    ✅ تحليل الأخبار والمشاعر
    ✅ مؤشر الخوف والطمع
    ✅ تنبيهات ذكية
    ✅ قاعدة بيانات محلية
    ✅ واجهة عربية احترافية
    
    **المصادر:**
    • CoinGecko API
    • Yahoo Finance
    • Alternative.me
    • RSS News Feeds
    """)

if __name__ == "__main__":
    main()