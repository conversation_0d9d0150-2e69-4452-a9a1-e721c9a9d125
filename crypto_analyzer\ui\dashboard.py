"""
لوحة التحكم الرئيسية لأداة تحليل العملات الرقمية
Main Dashboard for Cryptocurrency Analysis Tool
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List
import webbrowser
from pathlib import Path

try:
    from flask import Flask, render_template, jsonify, request
    from flask_socketio import SocketIO, emit
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False
    print("تحذير: Flask غير مثبت. سيتم إنشاء لوحة تحكم HTML بسيطة.")

class Dashboard:
    """لوحة التحكم الرئيسية"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.app = None
        self.socketio = None
        self.is_running = False
        
        # بيانات لوحة التحكم
        self.dashboard_data = {
            'market_overview': {},
            'portfolio_summary': {},
            'active_strategies': {},
            'recent_news': [],
            'predictions': {},
            'performance_metrics': {},
            'alerts': []
        }
        
        if FLASK_AVAILABLE:
            self._setup_flask_app()
        else:
            self._setup_html_dashboard()
    
    def _setup_flask_app(self):
        """إعداد تطبيق Flask"""
        try:
            self.app = Flask(__name__, 
                           template_folder='templates',
                           static_folder='static')
            self.app.config['SECRET_KEY'] = 'crypto_analyzer_secret_key'
            self.socketio = SocketIO(self.app, cors_allowed_origins="*")
            
            # تسجيل المسارات
            self._register_routes()
            self._register_socketio_events()
            
        except Exception as e:
            self.logger.error(f"خطأ في إعداد Flask: {e}")
    
    def _register_routes(self):
        """تسجيل مسارات Flask"""
        
        @self.app.route('/')
        def index():
            """الصفحة الرئيسية"""
            return render_template('dashboard.html', data=self.dashboard_data)
        
        @self.app.route('/api/data')
        def get_data():
            """API للحصول على البيانات"""
            return jsonify(self.dashboard_data)
        
        @self.app.route('/api/portfolio')
        def get_portfolio():
            """API للحصول على بيانات المحفظة"""
            return jsonify(self.dashboard_data.get('portfolio_summary', {}))
        
        @self.app.route('/api/strategies')
        def get_strategies():
            """API للحصول على الاستراتيجيات"""
            return jsonify(self.dashboard_data.get('active_strategies', {}))
        
        @self.app.route('/api/news')
        def get_news():
            """API للحصول على الأخبار"""
            return jsonify(self.dashboard_data.get('recent_news', []))
        
        @self.app.route('/api/predictions')
        def get_predictions():
            """API للحصول على التنبؤات"""
            return jsonify(self.dashboard_data.get('predictions', {}))
    
    def _register_socketio_events(self):
        """تسجيل أحداث SocketIO"""
        
        @self.socketio.on('connect')
        def handle_connect():
            """عند الاتصال"""
            self.logger.info("عميل جديد متصل بلوحة التحكم")
            emit('data_update', self.dashboard_data)
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            """عند قطع الاتصال"""
            self.logger.info("عميل منقطع عن لوحة التحكم")
        
        @self.socketio.on('request_update')
        def handle_request_update():
            """طلب تحديث البيانات"""
            emit('data_update', self.dashboard_data)
    
    def _setup_html_dashboard(self):
        """إعداد لوحة تحكم HTML بسيطة"""
        try:
            # إنشاء مجلد templates إذا لم يكن موجوداً
            templates_dir = Path("crypto_analyzer/ui/templates")
            templates_dir.mkdir(parents=True, exist_ok=True)
            
            # إنشاء ملف HTML بسيط
            self._create_simple_html()
            
        except Exception as e:
            self.logger.error(f"خطأ في إعداد HTML Dashboard: {e}")
    
    def _create_simple_html(self):
        """إنشاء ملف HTML بسيط"""
        html_content = """
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أداة تحليل العملات الرقمية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #7f8c8d;
            font-size: 1.2em;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.4em;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        
        .metric {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .metric:last-child {
            border-bottom: none;
        }
        
        .metric-label {
            font-weight: 600;
            color: #34495e;
        }
        
        .metric-value {
            color: #27ae60;
            font-weight: bold;
        }
        
        .metric-value.negative {
            color: #e74c3c;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        
        .status-active {
            background-color: #27ae60;
        }
        
        .status-inactive {
            background-color: #e74c3c;
        }
        
        .status-warning {
            background-color: #f39c12;
        }
        
        .news-item {
            padding: 10px 0;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .news-item:last-child {
            border-bottom: none;
        }
        
        .news-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .news-meta {
            font-size: 0.9em;
            color: #7f8c8d;
        }
        
        .prediction-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            border-left: 4px solid #3498db;
        }
        
        .prediction-coin {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8px;
        }
        
        .prediction-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            font-size: 0.9em;
        }
        
        .update-time {
            text-align: center;
            color: #7f8c8d;
            font-size: 0.9em;
            margin-top: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .refresh-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background 0.3s ease;
        }
        
        .refresh-btn:hover {
            background: #2980b9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 أداة تحليل العملات الرقمية المتقدمة</h1>
            <p>مراقبة وتحليل وتنبؤ أسعار العملات الرقمية في الوقت الفعلي</p>
            <button class="refresh-btn" onclick="loadData()">تحديث البيانات</button>
        </div>
        
        <div class="dashboard-grid">
            <!-- ملخص المحفظة -->
            <div class="card">
                <h3>📊 ملخص المحفظة</h3>
                <div id="portfolio-summary">
                    <div class="loading">
                        <div class="spinner"></div>
                        جاري تحميل بيانات المحفظة...
                    </div>
                </div>
            </div>
            
            <!-- نظرة عامة على السوق -->
            <div class="card">
                <h3>🌍 نظرة عامة على السوق</h3>
                <div id="market-overview">
                    <div class="loading">
                        <div class="spinner"></div>
                        جاري تحميل بيانات السوق...
                    </div>
                </div>
            </div>
            
            <!-- الاستراتيجيات النشطة -->
            <div class="card">
                <h3>⚡ الاستراتيجيات النشطة</h3>
                <div id="active-strategies">
                    <div class="loading">
                        <div class="spinner"></div>
                        جاري تحميل الاستراتيجيات...
                    </div>
                </div>
            </div>
            
            <!-- التنبؤات -->
            <div class="card">
                <h3>🔮 التنبؤات</h3>
                <div id="predictions">
                    <div class="loading">
                        <div class="spinner"></div>
                        جاري تحميل التنبؤات...
                    </div>
                </div>
            </div>
            
            <!-- الأخبار الحديثة -->
            <div class="card">
                <h3>📰 الأخبار الحديثة</h3>
                <div id="recent-news">
                    <div class="loading">
                        <div class="spinner"></div>
                        جاري تحميل الأخبار...
                    </div>
                </div>
            </div>
            
            <!-- مؤشرات الأداء -->
            <div class="card">
                <h3>📈 مؤشرات الأداء</h3>
                <div id="performance-metrics">
                    <div class="loading">
                        <div class="spinner"></div>
                        جاري تحميل مؤشرات الأداء...
                    </div>
                </div>
            </div>
        </div>
        
        <div class="update-time" id="update-time">
            آخر تحديث: جاري التحميل...
        </div>
    </div>
    
    <script>
        // تحميل البيانات
        async function loadData() {
            try {
                // محاكاة تحميل البيانات (في التطبيق الحقيقي، ستأتي من API)
                const data = await simulateDataLoad();
                updateDashboard(data);
            } catch (error) {
                console.error('خطأ في تحميل البيانات:', error);
            }
        }
        
        // محاكاة تحميل البيانات
        async function simulateDataLoad() {
            // محاكاة تأخير الشبكة
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            return {
                portfolio_summary: {
                    total_value: 12500.50,
                    cash_balance: 2500.00,
                    unrealized_pnl: 1250.50,
                    total_return: 0.125,
                    positions_count: 5
                },
                market_overview: {
                    total_market_cap: "2.1T",
                    btc_dominance: 42.5,
                    fear_greed_index: 65,
                    trending_coins: ["Bitcoin", "Ethereum", "Solana"]
                },
                active_strategies: {
                    bitcoin: { type: "تتبع الاتجاه", status: "نشط", performance: 15.2 },
                    ethereum: { type: "الزخم", status: "نشط", performance: 8.7 },
                    solana: { type: "العودة للمتوسط", status: "متوقف", performance: -2.1 }
                },
                predictions: {
                    bitcoin: { direction: "صاعد", confidence: 85, target_price: 52000 },
                    ethereum: { direction: "صاعد", confidence: 78, target_price: 3200 },
                    solana: { direction: "هابط", confidence: 65, target_price: 95 }
                },
                recent_news: [
                    { title: "البيتكوين يكسر مستوى 50000 دولار", source: "CoinDesk", time: "منذ ساعة" },
                    { title: "إيثيريوم يستعد لتحديث جديد", source: "CoinTelegraph", time: "منذ 3 ساعات" },
                    { title: "سولانا تشهد نمواً في النشاط", source: "Decrypt", time: "منذ 5 ساعات" }
                ],
                performance_metrics: {
                    sharpe_ratio: 1.85,
                    max_drawdown: -8.5,
                    win_rate: 0.72,
                    volatility: 0.25
                }
            };
        }
        
        // تحديث لوحة التحكم
        function updateDashboard(data) {
            updatePortfolioSummary(data.portfolio_summary);
            updateMarketOverview(data.market_overview);
            updateActiveStrategies(data.active_strategies);
            updatePredictions(data.predictions);
            updateRecentNews(data.recent_news);
            updatePerformanceMetrics(data.performance_metrics);
            updateTime();
        }
        
        function updatePortfolioSummary(data) {
            const container = document.getElementById('portfolio-summary');
            container.innerHTML = `
                <div class="metric">
                    <span class="metric-label">إجمالي القيمة:</span>
                    <span class="metric-value">$${data.total_value.toLocaleString()}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">الرصيد النقدي:</span>
                    <span class="metric-value">$${data.cash_balance.toLocaleString()}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">الأرباح غير المحققة:</span>
                    <span class="metric-value ${data.unrealized_pnl >= 0 ? '' : 'negative'}">
                        $${data.unrealized_pnl.toLocaleString()}
                    </span>
                </div>
                <div class="metric">
                    <span class="metric-label">العائد الإجمالي:</span>
                    <span class="metric-value ${data.total_return >= 0 ? '' : 'negative'}">
                        ${(data.total_return * 100).toFixed(2)}%
                    </span>
                </div>
                <div class="metric">
                    <span class="metric-label">عدد المراكز:</span>
                    <span class="metric-value">${data.positions_count}</span>
                </div>
            `;
        }
        
        function updateMarketOverview(data) {
            const container = document.getElementById('market-overview');
            container.innerHTML = `
                <div class="metric">
                    <span class="metric-label">القيمة السوقية الإجمالية:</span>
                    <span class="metric-value">$${data.total_market_cap}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">هيمنة البيتكوين:</span>
                    <span class="metric-value">${data.btc_dominance}%</span>
                </div>
                <div class="metric">
                    <span class="metric-label">مؤشر الخوف والطمع:</span>
                    <span class="metric-value">${data.fear_greed_index}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">العملات الرائجة:</span>
                    <span class="metric-value">${data.trending_coins.join(', ')}</span>
                </div>
            `;
        }
        
        function updateActiveStrategies(data) {
            const container = document.getElementById('active-strategies');
            let html = '';
            
            for (const [coin, strategy] of Object.entries(data)) {
                const statusClass = strategy.status === 'نشط' ? 'status-active' : 
                                  strategy.status === 'متوقف' ? 'status-inactive' : 'status-warning';
                const performanceClass = strategy.performance >= 0 ? '' : 'negative';
                
                html += `
                    <div class="metric">
                        <span class="metric-label">
                            ${coin.toUpperCase()}
                            <span class="status-indicator ${statusClass}"></span>
                        </span>
                        <span class="metric-value ${performanceClass}">
                            ${strategy.performance > 0 ? '+' : ''}${strategy.performance}%
                        </span>
                    </div>
                `;
            }
            
            container.innerHTML = html;
        }
        
        function updatePredictions(data) {
            const container = document.getElementById('predictions');
            let html = '';
            
            for (const [coin, prediction] of Object.entries(data)) {
                html += `
                    <div class="prediction-item">
                        <div class="prediction-coin">${coin.toUpperCase()}</div>
                        <div class="prediction-details">
                            <div>الاتجاه: ${prediction.direction}</div>
                            <div>الثقة: ${prediction.confidence}%</div>
                            <div>السعر المستهدف: $${prediction.target_price.toLocaleString()}</div>
                        </div>
                    </div>
                `;
            }
            
            container.innerHTML = html;
        }
        
        function updateRecentNews(data) {
            const container = document.getElementById('recent-news');
            let html = '';
            
            data.forEach(news => {
                html += `
                    <div class="news-item">
                        <div class="news-title">${news.title}</div>
                        <div class="news-meta">${news.source} - ${news.time}</div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        function updatePerformanceMetrics(data) {
            const container = document.getElementById('performance-metrics');
            container.innerHTML = `
                <div class="metric">
                    <span class="metric-label">نسبة شارب:</span>
                    <span class="metric-value">${data.sharpe_ratio}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">أقصى انخفاض:</span>
                    <span class="metric-value negative">${data.max_drawdown}%</span>
                </div>
                <div class="metric">
                    <span class="metric-label">معدل النجاح:</span>
                    <span class="metric-value">${(data.win_rate * 100).toFixed(1)}%</span>
                </div>
                <div class="metric">
                    <span class="metric-label">التقلبات:</span>
                    <span class="metric-value">${(data.volatility * 100).toFixed(1)}%</span>
                </div>
            `;
        }
        
        function updateTime() {
            const container = document.getElementById('update-time');
            const now = new Date();
            container.textContent = `آخر تحديث: ${now.toLocaleString('ar-EG')}`;
        }
        
        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', loadData);
        
        // تحديث تلقائي كل 30 ثانية
        setInterval(loadData, 30000);
    </script>
</body>
</html>
        """
        
        # حفظ ملف HTML
        html_file = Path("crypto_analyzer/ui/dashboard.html")
        html_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        self.logger.info(f"تم إنشاء لوحة التحكم في {html_file}")
    
    async def start_server(self):
        """بدء تشغيل خادم لوحة التحكم"""
        try:
            if FLASK_AVAILABLE and self.app:
                self.is_running = True
                self.logger.info("🌐 بدء تشغيل خادم لوحة التحكم على http://localhost:5000")
                
                # تشغيل الخادم في thread منفصل
                import threading
                server_thread = threading.Thread(
                    target=lambda: self.socketio.run(self.app, host='0.0.0.0', port=5000, debug=False)
                )
                server_thread.daemon = True
                server_thread.start()
                
                # فتح المتصفح
                await asyncio.sleep(2)  # انتظار بدء الخادم
                webbrowser.open('http://localhost:5000')
                
            else:
                # فتح ملف HTML مباشرة
                html_file = Path("crypto_analyzer/ui/dashboard.html").absolute()
                if html_file.exists():
                    self.logger.info(f"🌐 فتح لوحة التحكم: {html_file}")
                    webbrowser.open(f'file://{html_file}')
                else:
                    self.logger.error("ملف لوحة التحكم غير موجود")
        
        except Exception as e:
            self.logger.error(f"خطأ في بدء تشغيل لوحة التحكم: {e}")
    
    def update_data(self, data_type: str, data: Dict):
        """تحديث بيانات لوحة التحكم"""
        try:
            self.dashboard_data[data_type] = data
            
            # إرسال التحديث للعملاء المتصلين (إذا كان Flask متاحاً)
            if FLASK_AVAILABLE and self.socketio and self.is_running:
                self.socketio.emit('data_update', {data_type: data})
            
            # حفظ البيانات في ملف JSON
            self._save_dashboard_data()
            
        except Exception as e:
            self.logger.error(f"خطأ في تحديث بيانات لوحة التحكم: {e}")
    
    def _save_dashboard_data(self):
        """حفظ بيانات لوحة التحكم"""
        try:
            data_file = Path("crypto_analyzer/ui/dashboard_data.json")
            data_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(data_file, 'w', encoding='utf-8') as f:
                json.dump(self.dashboard_data, f, ensure_ascii=False, indent=2, default=str)
        
        except Exception as e:
            self.logger.error(f"خطأ في حفظ بيانات لوحة التحكم: {e}")
    
    def add_alert(self, alert_type: str, message: str, severity: str = 'info'):
        """إضافة تنبيه"""
        try:
            alert = {
                'type': alert_type,
                'message': message,
                'severity': severity,
                'timestamp': datetime.now(),
                'id': len(self.dashboard_data['alerts'])
            }
            
            self.dashboard_data['alerts'].append(alert)
            
            # الاحتفاظ بآخر 50 تنبيه فقط
            if len(self.dashboard_data['alerts']) > 50:
                self.dashboard_data['alerts'] = self.dashboard_data['alerts'][-50:]
            
            # إرسال التنبيه للعملاء المتصلين
            if FLASK_AVAILABLE and self.socketio and self.is_running:
                self.socketio.emit('new_alert', alert)
            
            self.logger.info(f"تنبيه جديد: {message}")
        
        except Exception as e:
            self.logger.error(f"خطأ في إضافة التنبيه: {e}")
    
    def get_dashboard_url(self) -> str:
        """الحصول على رابط لوحة التحكم"""
        if FLASK_AVAILABLE and self.is_running:
            return "http://localhost:5000"
        else:
            html_file = Path("crypto_analyzer/ui/dashboard.html").absolute()
            return f"file://{html_file}"
    
    def stop_server(self):
        """إيقاف خادم لوحة التحكم"""
        try:
            self.is_running = False
            self.logger.info("تم إيقاف خادم لوحة التحكم")
        
        except Exception as e:
            self.logger.error(f"خطأ في إيقاف خادم لوحة التحكم: {e}")