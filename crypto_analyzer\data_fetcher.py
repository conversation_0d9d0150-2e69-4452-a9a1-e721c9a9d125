"""
وحدة جلب بيانات العملات الرقمية
Cryptocurrency Data Fetcher Module
"""

import aiohttp
import asyncio
import logging
from typing import Dict, List, Optional
from datetime import datetime, timedelta
import pandas as pd
import json

class CryptoDataFetcher:
    """فئة جلب بيانات العملات الرقمية من مصادر متعددة"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # APIs المجانية للعملات الرقمية
        self.apis = {
            'coingecko': 'https://api.coingecko.com/api/v3',
            'coinpaprika': 'https://api.coinpaprika.com/v1',
            'binance': 'https://api.binance.com/api/v3'
        }
        
        # معدل الطلبات (requests per minute)
        self.rate_limits = {
            'coingecko': 50,
            'coinpaprika': 25000,
            'binance': 1200
        }
        
        self.session = None
    
    async def __aenter__(self):
        """إنشاء جلسة HTTP"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """إغلاق جلسة HTTP"""
        if self.session:
            await self.session.close()
    
    async def get_market_data(self, coin_ids: List[str]) -> Dict:
        """جلب بيانات السوق للعملات المحددة"""
        if not self.session:
            self.session = aiohttp.ClientSession()
        
        try:
            # استخدام CoinGecko API
            coins_str = ','.join(coin_ids)
            url = f"{self.apis['coingecko']}/coins/markets"
            
            params = {
                'vs_currency': 'usd',
                'ids': coins_str,
                'order': 'market_cap_desc',
                'per_page': len(coin_ids),
                'page': 1,
                'sparkline': True,
                'price_change_percentage': '1h,24h,7d,30d'
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._process_market_data(data)
                else:
                    self.logger.error(f"خطأ في جلب بيانات السوق: {response.status}")
                    return {}
        
        except Exception as e:
            self.logger.error(f"خطأ في جلب بيانات السوق: {e}")
            return {}
    
    def _process_market_data(self, raw_data: List[Dict]) -> Dict:
        """معالجة بيانات السوق الخام"""
        processed_data = {}
        
        for coin in raw_data:
            coin_id = coin['id']
            processed_data[coin_id] = {
                'symbol': coin['symbol'].upper(),
                'name': coin['name'],
                'current_price': coin['current_price'],
                'market_cap': coin['market_cap'],
                'market_cap_rank': coin['market_cap_rank'],
                'volume_24h': coin['total_volume'],
                'price_change_24h': coin['price_change_24h'],
                'price_change_percentage_24h': coin['price_change_percentage_24h'],
                'price_change_percentage_7d': coin.get('price_change_percentage_7d_in_currency'),
                'price_change_percentage_30d': coin.get('price_change_percentage_30d_in_currency'),
                'circulating_supply': coin['circulating_supply'],
                'total_supply': coin['total_supply'],
                'max_supply': coin['max_supply'],
                'ath': coin['ath'],
                'ath_change_percentage': coin['ath_change_percentage'],
                'ath_date': coin['ath_date'],
                'atl': coin['atl'],
                'atl_change_percentage': coin['atl_change_percentage'],
                'atl_date': coin['atl_date'],
                'last_updated': coin['last_updated'],
                'sparkline_7d': coin.get('sparkline_in_7d', {}).get('price', [])
            }
        
        return processed_data
    
    async def get_historical_data(self, coin_id: str, days: int = 30) -> pd.DataFrame:
        """جلب البيانات التاريخية للعملة"""
        if not self.session:
            self.session = aiohttp.ClientSession()
        
        try:
            url = f"{self.apis['coingecko']}/coins/{coin_id}/market_chart"
            params = {
                'vs_currency': 'usd',
                'days': days,
                'interval': 'hourly' if days <= 30 else 'daily'
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._process_historical_data(data)
                else:
                    self.logger.error(f"خطأ في جلب البيانات التاريخية: {response.status}")
                    return pd.DataFrame()
        
        except Exception as e:
            self.logger.error(f"خطأ في جلب البيانات التاريخية: {e}")
            return pd.DataFrame()
    
    def _process_historical_data(self, raw_data: Dict) -> pd.DataFrame:
        """معالجة البيانات التاريخية"""
        try:
            prices = raw_data.get('prices', [])
            volumes = raw_data.get('total_volumes', [])
            market_caps = raw_data.get('market_caps', [])
            
            # تحويل إلى DataFrame
            df = pd.DataFrame({
                'timestamp': [datetime.fromtimestamp(p[0]/1000) for p in prices],
                'price': [p[1] for p in prices],
                'volume': [v[1] for v in volumes] if volumes else [0] * len(prices),
                'market_cap': [m[1] for m in market_caps] if market_caps else [0] * len(prices)
            })
            
            df.set_index('timestamp', inplace=True)
            return df
        
        except Exception as e:
            self.logger.error(f"خطأ في معالجة البيانات التاريخية: {e}")
            return pd.DataFrame()
    
    async def get_ohlcv_data(self, coin_id: str, days: int = 30) -> pd.DataFrame:
        """جلب بيانات OHLCV (Open, High, Low, Close, Volume)"""
        if not self.session:
            self.session = aiohttp.ClientSession()
        
        try:
            url = f"{self.apis['coingecko']}/coins/{coin_id}/ohlc"
            params = {
                'vs_currency': 'usd',
                'days': days
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._process_ohlcv_data(data)
                else:
                    self.logger.error(f"خطأ في جلب بيانات OHLCV: {response.status}")
                    return pd.DataFrame()
        
        except Exception as e:
            self.logger.error(f"خطأ في جلب بيانات OHLCV: {e}")
            return pd.DataFrame()
    
    def _process_ohlcv_data(self, raw_data: List) -> pd.DataFrame:
        """معالجة بيانات OHLCV"""
        try:
            df = pd.DataFrame(raw_data, columns=['timestamp', 'open', 'high', 'low', 'close'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            return df
        
        except Exception as e:
            self.logger.error(f"خطأ في معالجة بيانات OHLCV: {e}")
            return pd.DataFrame()
    
    async def get_trending_coins(self) -> List[Dict]:
        """جلب العملات الرائجة"""
        if not self.session:
            self.session = aiohttp.ClientSession()
        
        try:
            url = f"{self.apis['coingecko']}/search/trending"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get('coins', [])
                else:
                    self.logger.error(f"خطأ في جلب العملات الرائجة: {response.status}")
                    return []
        
        except Exception as e:
            self.logger.error(f"خطأ في جلب العملات الرائجة: {e}")
            return []
    
    async def get_global_market_data(self) -> Dict:
        """جلب بيانات السوق العالمية"""
        if not self.session:
            self.session = aiohttp.ClientSession()
        
        try:
            url = f"{self.apis['coingecko']}/global"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get('data', {})
                else:
                    self.logger.error(f"خطأ في جلب بيانات السوق العالمية: {response.status}")
                    return {}
        
        except Exception as e:
            self.logger.error(f"خطأ في جلب بيانات السوق العالمية: {e}")
            return {}
    
    async def get_fear_greed_index(self) -> Dict:
        """جلب مؤشر الخوف والطمع"""
        if not self.session:
            self.session = aiohttp.ClientSession()
        
        try:
            # استخدام Alternative.me API
            url = "https://api.alternative.me/fng/"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('data'):
                        return data['data'][0]
                    return {}
                else:
                    self.logger.error(f"خطأ في جلب مؤشر الخوف والطمع: {response.status}")
                    return {}
        
        except Exception as e:
            self.logger.error(f"خطأ في جلب مؤشر الخوف والطمع: {e}")
            return {}
    
    async def get_exchange_rates(self) -> Dict:
        """جلب أسعار صرف العملات الرئيسية"""
        if not self.session:
            self.session = aiohttp.ClientSession()
        
        try:
            url = f"{self.apis['coingecko']}/exchange_rates"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get('rates', {})
                else:
                    self.logger.error(f"خطأ في جلب أسعار الصرف: {response.status}")
                    return {}
        
        except Exception as e:
            self.logger.error(f"خطأ في جلب أسعار الصرف: {e}")
            return {}
    
    async def search_coins(self, query: str) -> List[Dict]:
        """البحث عن العملات الرقمية"""
        if not self.session:
            self.session = aiohttp.ClientSession()
        
        try:
            url = f"{self.apis['coingecko']}/search"
            params = {'query': query}
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get('coins', [])
                else:
                    self.logger.error(f"خطأ في البحث عن العملات: {response.status}")
                    return []
        
        except Exception as e:
            self.logger.error(f"خطأ في البحث عن العملات: {e}")
            return []
    
    async def get_coin_info(self, coin_id: str) -> Dict:
        """جلب معلومات مفصلة عن العملة"""
        if not self.session:
            self.session = aiohttp.ClientSession()
        
        try:
            url = f"{self.apis['coingecko']}/coins/{coin_id}"
            params = {
                'localization': False,
                'tickers': False,
                'market_data': True,
                'community_data': True,
                'developer_data': True
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    self.logger.error(f"خطأ في جلب معلومات العملة: {response.status}")
                    return {}
        
        except Exception as e:
            self.logger.error(f"خطأ في جلب معلومات العملة: {e}")
            return {}
    
    def save_data_to_file(self, data: Dict, filename: str):
        """حفظ البيانات في ملف"""
        try:
            with open(f"data/{filename}", 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)
            self.logger.info(f"تم حفظ البيانات في {filename}")
        except Exception as e:
            self.logger.error(f"خطأ في حفظ البيانات: {e}")
    
    async def close(self):
        """إغلاق الجلسة"""
        if self.session:
            await self.session.close()