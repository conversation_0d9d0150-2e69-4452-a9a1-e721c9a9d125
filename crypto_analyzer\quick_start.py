#!/usr/bin/env python3
"""
تشغيل سريع لأداة تحليل العملات الرقمية
Quick Start for Cryptocurrency Analysis Tool
"""

import asyncio
import sys
import webbrowser
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def print_welcome():
    """طباعة رسالة الترحيب"""
    welcome = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🚀 أداة تحليل العملات الرقمية المتقدمة 🚀           ║
    ║                                                              ║
    ║                    التشغيل السريع                           ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    
    مرحباً بك في أداة تحليل العملات الرقمية المتقدمة!
    
    هذه الأداة تقوم بـ:
    🔍 مراقبة أسعار العملات الرقمية في الوقت الفعلي
    📊 التحليل الفني المتقدم باستخدام أكثر من 15 مؤشر
    🔮 التنبؤ بالأسعار باستخدام الذكاء الاصطناعي
    📰 تحليل الأخبار والمشاعر
    ⚡ بناء استراتيجيات التداول التلقائية
    💼 إدارة المحفظة الذكية
    🌐 لوحة تحكم تفاعلية
    
    """
    print(welcome)

async def quick_demo():
    """عرض توضيحي سريع"""
    print("🎬 بدء العرض التوضيحي السريع...")
    print("="*50)
    
    try:
        # استيراد الوحدات
        from data_fetcher import CryptoDataFetcher
        from technical_analyzer import TechnicalAnalyzer
        from config import create_required_directories
        
        # إنشاء المجلدات المطلوبة
        create_required_directories()
        print("✅ تم إنشاء المجلدات المطلوبة")
        
        # اختبار جلب البيانات
        print("\n🔍 جلب بيانات البيتكوين...")
        
        async with CryptoDataFetcher() as fetcher:
            market_data = await fetcher.get_market_data(['bitcoin'])
            
            if 'bitcoin' in market_data:
                btc_data = market_data['bitcoin']
                print(f"✅ تم جلب بيانات البيتكوين بنجاح!")
                print(f"   💰 السعر الحالي: ${btc_data['current_price']:,.2f}")
                print(f"   📊 القيمة السوقية: ${btc_data['market_cap']:,}")
                print(f"   📈 التغيير 24 ساعة: {btc_data['price_change_percentage_24h']:+.2f}%")
                print(f"   🏆 الترتيب: #{btc_data['market_cap_rank']}")
                
                # تحليل فني سريع
                print("\n📊 إجراء تحليل فني سريع...")
                analyzer = TechnicalAnalyzer()
                analysis = await analyzer.analyze('bitcoin', btc_data)
                
                if analysis and 'recommendation' in analysis:
                    recommendation = analysis['recommendation']
                    print(f"✅ التحليل الفني مكتمل!")
                    print(f"   🎯 التوصية: {recommendation}")
                    
                    if 'strength_score' in analysis:
                        score = analysis['strength_score'].get('percentage', 0)
                        level = analysis['strength_score'].get('level', 'غير محدد')
                        print(f"   💪 قوة الإشارة: {score:.1f}% ({level})")
                
                print("\n🎉 العرض التوضيحي مكتمل بنجاح!")
                print("🚀 الأداة جاهزة للاستخدام الكامل")
                
            else:
                print("❌ فشل في جلب بيانات البيتكوين")
                print("🔧 تحقق من اتصال الإنترنت وحاول مرة أخرى")
    
    except Exception as e:
        print(f"❌ خطأ في العرض التوضيحي: {e}")
        print("💡 تأكد من تثبيت جميع المتطلبات: pip install -r requirements.txt")

def show_quick_menu():
    """عرض القائمة السريعة"""
    menu = """
    📋 خيارات التشغيل السريع:
    
    1️⃣  عرض توضيحي سريع (اختبار البيتكوين)
    2️⃣  تشغيل لوحة التحكم
    3️⃣  تشغيل الأداة الكاملة
    4️⃣  اختبار جميع الوحدات
    5️⃣  عرض الدليل
    0️⃣  خروج
    
    """
    print(menu)

async def run_dashboard():
    """تشغيل لوحة التحكم"""
    print("🌐 تشغيل لوحة التحكم...")
    
    try:
        from ui.dashboard import Dashboard
        
        dashboard = Dashboard()
        print("✅ تم إنشاء لوحة التحكم")
        print("🌐 جاري فتح المتصفح...")
        
        await dashboard.start_server()
        
        print("✅ لوحة التحكم تعمل الآن!")
        print("🔗 الرابط: http://localhost:5000")
        print("⏹️  اضغط Ctrl+C للإيقاف")
        
        # إبقاء البرنامج يعمل
        try:
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            print("\n⏹️  تم إيقاف لوحة التحكم")
            dashboard.stop_server()
    
    except Exception as e:
        print(f"❌ خطأ في تشغيل لوحة التحكم: {e}")

async def run_full_analyzer():
    """تشغيل الأداة الكاملة"""
    print("🚀 تشغيل أداة التحليل الكاملة...")
    
    try:
        from main import CryptoAnalyzer
        
        analyzer = CryptoAnalyzer()
        print("✅ تم إنشاء محلل العملات الرقمية")
        print("🔄 بدء المراقبة والتحليل...")
        print("⏹️  اضغط Ctrl+C للإيقاف")
        
        await analyzer.start_monitoring()
    
    except KeyboardInterrupt:
        print("\n⏹️  تم إيقاف المحلل")
    except Exception as e:
        print(f"❌ خطأ في تشغيل المحلل: {e}")

async def run_tests():
    """تشغيل الاختبارات"""
    print("🧪 تشغيل اختبارات الأداة...")
    
    try:
        from test_analyzer import CryptoAnalyzerTester
        
        tester = CryptoAnalyzerTester()
        await tester.run_all_tests()
    
    except Exception as e:
        print(f"❌ خطأ في تشغيل الاختبارات: {e}")

def show_guide():
    """عرض الدليل السريع"""
    guide = """
    📚 دليل الاستخدام السريع
    ========================
    
    🎯 الهدف:
    أداة شاملة لتحليل العملات الرقمية تقوم بمراقبة الأسواق،
    التحليل الفني، التنبؤ بالأسعار، وبناء استراتيجيات التداول.
    
    🚀 البدء السريع:
    1. تأكد من تثبيت Python 3.8+
    2. ثبت المتطلبات: pip install -r requirements.txt
    3. شغل العرض التوضيحي للتأكد من عمل الأداة
    4. استخدم لوحة التحكم للمراقبة التفاعلية
    
    📊 الميزات الرئيسية:
    • مراقبة 10+ عملات رقمية رئيسية
    • 15+ مؤشر فني متقدم
    • تنبؤات ذكية باستخدام AI
    • تحليل الأخبار والمشاعر
    • 8 استراتيجيات تداول مختلفة
    • إدارة محفظة ذكية
    
    🔧 الملفات المهمة:
    • run.py - التشغيل الكامل مع قائمة تفاعلية
    • quick_start.py - التشغيل السريع (هذا الملف)
    • test_analyzer.py - اختبار جميع الوحدات
    • config.py - إعدادات النظام
    • README.md - التوثيق الكامل
    
    ⚠️  تحذيرات مهمة:
    • هذه الأداة للأغراض التعليمية فقط
    • لا تستخدم أموال حقيقية بناءً على التنبؤات
    • استشر خبير مالي قبل الاستثمار
    
    🆘 الدعم:
    • تحقق من ملف README.md للتوثيق الكامل
    • راجع ملفات السجلات في مجلد logs/
    • تأكد من اتصال الإنترنت
    
    """
    print(guide)

async def main():
    """الدالة الرئيسية"""
    print_welcome()
    
    while True:
        try:
            show_quick_menu()
            choice = input("اختر خيار (0-5): ").strip()
            
            if choice == '0':
                print("👋 شكراً لاستخدام أداة تحليل العملات الرقمية!")
                break
            
            elif choice == '1':
                await quick_demo()
                input("\nاضغط Enter للمتابعة...")
            
            elif choice == '2':
                await run_dashboard()
            
            elif choice == '3':
                await run_full_analyzer()
            
            elif choice == '4':
                await run_tests()
                input("\nاضغط Enter للمتابعة...")
            
            elif choice == '5':
                show_guide()
                input("\nاضغط Enter للمتابعة...")
            
            else:
                print("❌ خيار غير صحيح، يرجى المحاولة مرة أخرى")
        
        except KeyboardInterrupt:
            print("\n👋 تم إنهاء البرنامج")
            break
        
        except Exception as e:
            print(f"❌ خطأ غير متوقع: {e}")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء البرنامج")
    except Exception as e:
        print(f"❌ خطأ في تشغيل البرنامج: {e}")