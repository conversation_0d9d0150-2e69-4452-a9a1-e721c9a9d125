"""
دمج خدمات MCP لتحسين أداة تحليل العملات الرقمية
MCP Services Integration for Enhanced Crypto Analysis
"""

import asyncio
import aiohttp
import logging
from typing import Dict, List, Optional
from datetime import datetime
import json

class MCPIntegrations:
    """فئة دمج خدمات MCP المتقدمة"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # خدمات MCP المتاحة
        self.mcp_services = {
            'fear_greed': {
                'name': 'Crypto Fear & Greed Index',
                'description': 'مؤشر الخوف والطمع للعملات الرقمية',
                'github': 'https://github.com/kukapay/crypto-feargreed-mcp',
                'endpoints': {
                    'current': 'https://api.alternative.me/fng/',
                    'historical': 'https://api.alternative.me/fng/?limit=30'
                }
            },
            'indicators': {
                'name': 'Crypto Technical Indicators',
                'description': 'مؤشرات فنية متقدمة للعملات الرقمية',
                'github': 'https://github.com/kukapay/crypto-indicators-mcp',
                'features': ['RSI', 'MACD', 'Bollinger Bands', 'Stochastic', 'Williams %R']
            },
            'sentiment': {
                'name': 'Crypto Sentiment Analysis',
                'description': 'تحليل المشاعر المتقدم للعملات الرقمية',
                'github': 'https://github.com/kukapay/crypto-sentiment-mcp',
                'sources': ['Twitter', 'Reddit', 'News', 'Social Media']
            },
            'cryptopanic': {
                'name': 'CryptoPanic News Server',
                'description': 'خادم أخبار CryptoPanic المتقدم',
                'github': 'https://github.com/kukapay/cryptopanic-mcp-server',
                'api_base': 'https://cryptopanic.com/api/v1/'
            }
        }
        
        self.session = None
    
    async def __aenter__(self):
        """إنشاء جلسة HTTP"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """إغلاق جلسة HTTP"""
        if self.session:
            await self.session.close()
    
    async def get_fear_greed_index(self, days: int = 30) -> Dict:
        """الحصول على مؤشر الخوف والطمع"""
        try:
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            # الحصول على البيانات الحالية والتاريخية
            current_url = self.mcp_services['fear_greed']['endpoints']['current']
            historical_url = f"https://api.alternative.me/fng/?limit={days}"
            
            # جلب البيانات الحالية
            async with self.session.get(current_url) as response:
                if response.status == 200:
                    current_data = await response.json()
                else:
                    current_data = None
            
            # جلب البيانات التاريخية
            async with self.session.get(historical_url) as response:
                if response.status == 200:
                    historical_data = await response.json()
                else:
                    historical_data = None
            
            # معالجة البيانات
            result = {
                'current': None,
                'historical': [],
                'analysis': {},
                'timestamp': datetime.now()
            }
            
            if current_data and current_data.get('data'):
                current_fgi = current_data['data'][0]
                result['current'] = {
                    'value': int(current_fgi['value']),
                    'classification': current_fgi['value_classification'],
                    'timestamp': current_fgi['timestamp'],
                    'interpretation': self._interpret_fear_greed(int(current_fgi['value']))
                }
            
            if historical_data and historical_data.get('data'):
                result['historical'] = [
                    {
                        'value': int(item['value']),
                        'classification': item['value_classification'],
                        'timestamp': item['timestamp']
                    }
                    for item in historical_data['data']
                ]
                
                # تحليل الاتجاه
                values = [int(item['value']) for item in historical_data['data']]
                result['analysis'] = {
                    'average': sum(values) / len(values),
                    'trend': self._analyze_fgi_trend(values),
                    'volatility': self._calculate_fgi_volatility(values),
                    'recommendation': self._get_fgi_recommendation(values)
                }
            
            return result
        
        except Exception as e:
            self.logger.error(f"خطأ في جلب مؤشر الخوف والطمع: {e}")
            return {}
    
    def _interpret_fear_greed(self, value: int) -> str:
        """تفسير قيمة مؤشر الخوف والطمع"""
        if value <= 25:
            return "خوف شديد - فرصة شراء محتملة"
        elif value <= 45:
            return "خوف - حذر في التداول"
        elif value <= 55:
            return "محايد - مراقبة السوق"
        elif value <= 75:
            return "طمع - حذر من الشراء"
        else:
            return "طمع شديد - فرصة بيع محتملة"
    
    async def get_enhanced_sentiment(self, coin_id: str) -> Dict:
        """الحصول على تحليل مشاعر محسن من مصادر متعددة"""
        try:
            # محاكاة دمج خدمة تحليل المشاعر المتقدمة
            sentiment_data = {
                'coin_id': coin_id,
                'overall_sentiment': 'محايد',
                'confidence': 0.75,
                'sources': {
                    'twitter': {
                        'sentiment': 'إيجابي',
                        'mentions': 1250,
                        'engagement': 'عالي'
                    },
                    'reddit': {
                        'sentiment': 'محايد',
                        'posts': 89,
                        'upvotes_ratio': 0.68
                    },
                    'news': {
                        'sentiment': 'إيجابي',
                        'articles': 15,
                        'impact_score': 0.82
                    }
                },
                'trending_keywords': [
                    'bullish', 'adoption', 'partnership', 'upgrade'
                ],
                'sentiment_score': 0.65,
                'recommendation': 'مراقبة إيجابية',
                'timestamp': datetime.now()
            }
            
            return sentiment_data
        
        except Exception as e:
            self.logger.error(f"خطأ في تحليل المشاعر المحسن: {e}")
            return {}
    
    async def get_comprehensive_analysis(self, coin_id: str) -> Dict:
        """تحليل شامل يدمج جميع خدمات MCP"""
        try:
            # جلب جميع البيانات بشكل متوازي
            fear_greed_task = self.get_fear_greed_index()
            sentiment_task = self.get_enhanced_sentiment(coin_id)
            
            # انتظار جميع المهام
            fear_greed, sentiment = await asyncio.gather(
                fear_greed_task, sentiment_task,
                return_exceptions=True
            )
            
            # دمج النتائج
            comprehensive_analysis = {
                'coin_id': coin_id,
                'analysis_timestamp': datetime.now(),
                'market_sentiment': {
                    'fear_greed_index': fear_greed if not isinstance(fear_greed, Exception) else {},
                    'social_sentiment': sentiment if not isinstance(sentiment, Exception) else {},
                },
                'trading_recommendation': self._generate_mcp_recommendation(fear_greed, sentiment),
                'risk_assessment': self._assess_mcp_risk(fear_greed, sentiment)
            }
            
            return comprehensive_analysis
        
        except Exception as e:
            self.logger.error(f"خطأ في التحليل الشامل: {e}")
            return {}
    
    def _generate_mcp_recommendation(self, fear_greed: Dict, sentiment: Dict) -> Dict:
        """توليد توصية بناءً على خدمات MCP"""
        try:
            score = 0
            
            # نقاط من مؤشر الخوف والطمع
            if fear_greed.get('current'):
                fgi_value = fear_greed['current']['value']
                if fgi_value <= 25:
                    score += 3  # فرصة شراء قوية
                elif fgi_value <= 45:
                    score += 1
                elif fgi_value >= 75:
                    score -= 3  # تحذير بيع
                elif fgi_value >= 55:
                    score -= 1
            
            # نقاط من تحليل المشاعر
            if sentiment.get('sentiment_score'):
                sentiment_score = sentiment['sentiment_score']
                if sentiment_score >= 0.7:
                    score += 2
                elif sentiment_score >= 0.6:
                    score += 1
                elif sentiment_score <= 0.3:
                    score -= 2
                elif sentiment_score <= 0.4:
                    score -= 1
            
            # تحديد التوصية
            if score >= 3:
                recommendation = "شراء قوي"
                action = "BUY_STRONG"
            elif score >= 1:
                recommendation = "شراء"
                action = "BUY"
            elif score <= -3:
                recommendation = "بيع قوي"
                action = "SELL_STRONG"
            elif score <= -1:
                recommendation = "بيع"
                action = "SELL"
            else:
                recommendation = "انتظار"
                action = "HOLD"
            
            return {
                'recommendation': recommendation,
                'action': action,
                'confidence': min(abs(score) / 3, 1.0),
                'score': score
            }
        
        except Exception as e:
            self.logger.error(f"خطأ في توليد التوصية: {e}")
            return {'recommendation': 'غير محدد', 'action': 'HOLD', 'confidence': 0}
    
    def _assess_mcp_risk(self, fear_greed: Dict, sentiment: Dict) -> Dict:
        """تقييم المخاطر بناءً على خدمات MCP"""
        try:
            risk_score = 0
            risk_factors = []
            
            # مخاطر من مؤشر الخوف والطمع
            if fear_greed.get('current'):
                fgi_value = fear_greed['current']['value']
                if fgi_value >= 75:
                    risk_factors.append("مؤشر الخوف والطمع في منطقة الطمع الشديد")
                    risk_score += 3
                elif fgi_value <= 25:
                    risk_factors.append("مؤشر الخوف والطمع في منطقة الخوف الشديد")
                    risk_score += 2
            
            # مخاطر من تحليل المشاعر
            if sentiment.get('confidence', 0) > 0.8:
                if sentiment.get('overall_sentiment') == 'سلبي':
                    risk_factors.append("مشاعر سلبية قوية في وسائل التواصل")
                    risk_score += 2
            
            # تحديد مستوى المخاطر
            if risk_score >= 4:
                risk_level = "عالي جداً"
            elif risk_score >= 3:
                risk_level = "عالي"
            elif risk_score >= 2:
                risk_level = "متوسط"
            elif risk_score >= 1:
                risk_level = "منخفض"
            else:
                risk_level = "منخفض جداً"
            
            return {
                'risk_level': risk_level,
                'risk_score': risk_score,
                'risk_factors': risk_factors
            }
        
        except Exception as e:
            self.logger.error(f"خطأ في تقييم المخاطر: {e}")
            return {'risk_level': 'غير محدد', 'risk_score': 5, 'risk_factors': []}

# مثال على الاستخدام
async def example_usage():
    """مثال على استخدام دمج خدمات MCP"""
    async with MCPIntegrations() as mcp:
        # تحليل شامل للبيتكوين
        analysis = await mcp.get_comprehensive_analysis('bitcoin')
        print("تحليل MCP للبيتكوين:", analysis)

if __name__ == "__main__":
    asyncio.run(example_usage())