#!/usr/bin/env python3
"""
ملف التشغيل الرئيسي لأداة تحليل العملات الرقمية
Main Runner for Cryptocurrency Analysis Tool
"""

import asyncio
import sys
import os
import logging
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from main import CryptoAnalyzer

def setup_logging():
    """إعداد نظام السجلات"""
    # إنشاء مجلد السجلات
    logs_dir = project_root / "logs"
    logs_dir.mkdir(exist_ok=True)
    
    # إعداد التنسيق
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # إعداد السجلات
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        handlers=[
            logging.FileHandler(logs_dir / 'crypto_analyzer.log', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = [
        "data",
        "data/analysis",
        "data/news", 
        "data/predictions",
        "data/strategies",
        "data/portfolio",
        "logs",
        "ui/templates",
        "ui/static"
    ]
    
    for directory in directories:
        dir_path = project_root / directory
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"✅ تم إنشاء المجلد: {directory}")

def check_dependencies():
    """فحص المتطلبات"""
    required_packages = [
        'pandas', 'numpy', 'scikit-learn', 'aiohttp', 
        'textblob', 'beautifulsoup4', 'feedparser'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} مثبت")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} غير مثبت")
    
    if missing_packages:
        print(f"\n⚠️  المكتبات المفقودة: {', '.join(missing_packages)}")
        print("يرجى تثبيتها باستخدام:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def print_banner():
    """طباعة شعار البرنامج"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🚀 أداة تحليل العملات الرقمية المتقدمة 🚀           ║
    ║                                                              ║
    ║              Advanced Cryptocurrency Analysis Tool           ║
    ║                                                              ║
    ║  ✨ مراقبة العملات الرقمية في الوقت الفعلي                ║
    ║  📊 التحليل الفني المتقدم                                   ║
    ║  🔮 التنبؤ بالأسعار باستخدام الذكاء الاصطناعي             ║
    ║  📰 تحليل الأخبار والمشاعر                                ║
    ║  ⚡ بناء استراتيجيات التداول التلقائية                   ║
    ║  💼 إدارة المحفظة الذكية                                   ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def print_menu():
    """طباعة القائمة الرئيسية"""
    menu = """
    📋 القائمة الرئيسية:
    
    1️⃣  بدء التشغيل الكامل (مراقبة + تحليل + تنبؤ)
    2️⃣  تشغيل لوحة التحكم فقط
    3️⃣  تحليل عملة محددة
    4️⃣  عرض ملخص المحفظة
    5️⃣  تحديث الاستراتيجيات
    6️⃣  تحليل الأخبار
    7️⃣  إعدادات النظام
    8️⃣  مساعدة
    0️⃣  خروج
    
    """
    print(menu)

async def run_full_analysis():
    """تشغيل التحليل الكامل"""
    print("🚀 بدء التشغيل الكامل...")
    
    analyzer = CryptoAnalyzer()
    
    try:
        await analyzer.start_monitoring()
    except KeyboardInterrupt:
        print("\n⏹️  تم إيقاف التحليل بواسطة المستخدم")
        analyzer.stop_monitoring()
    except Exception as e:
        print(f"❌ خطأ في التشغيل: {e}")

async def run_dashboard_only():
    """تشغيل لوحة التحكم فقط"""
    print("🌐 بدء تشغيل لوحة التحكم...")
    
    from ui.dashboard import Dashboard
    
    dashboard = Dashboard()
    await dashboard.start_server()
    
    print("✅ تم تشغيل لوحة التحكم")
    print("🌐 يمكنك الوصول إليها عبر: http://localhost:5000")
    
    try:
        # إبقاء البرنامج يعمل
        while True:
            await asyncio.sleep(1)
    except KeyboardInterrupt:
        print("\n⏹️  تم إيقاف لوحة التحكم")
        dashboard.stop_server()

async def analyze_specific_coin():
    """تحليل عملة محددة"""
    print("🔍 تحليل عملة محددة")
    
    # قائمة العملات المتاحة
    available_coins = [
        'bitcoin', 'ethereum', 'binancecoin', 'cardano', 'solana',
        'polkadot', 'dogecoin', 'avalanche-2', 'chainlink', 'polygon'
    ]
    
    print("\n📋 العملات المتاحة:")
    for i, coin in enumerate(available_coins, 1):
        print(f"{i}. {coin}")
    
    try:
        choice = input("\nاختر رقم العملة (أو اكتب اسم العملة): ").strip()
        
        if choice.isdigit():
            coin_index = int(choice) - 1
            if 0 <= coin_index < len(available_coins):
                coin_id = available_coins[coin_index]
            else:
                print("❌ رقم غير صحيح")
                return
        else:
            coin_id = choice.lower()
        
        print(f"🔍 جاري تحليل {coin_id}...")
        
        # تشغيل التحليل
        from data_fetcher import CryptoDataFetcher
        from technical_analyzer import TechnicalAnalyzer
        from prediction_engine import PredictionEngine
        
        async with CryptoDataFetcher() as fetcher:
            # جلب البيانات
            market_data = await fetcher.get_market_data([coin_id])
            
            if coin_id not in market_data:
                print(f"❌ لم يتم العثور على بيانات للعملة {coin_id}")
                return
            
            coin_data = market_data[coin_id]
            
            # التحليل الفني
            analyzer = TechnicalAnalyzer()
            technical_analysis = await analyzer.analyze(coin_id, coin_data)
            
            # التنبؤ
            predictor = PredictionEngine()
            prediction = await predictor.predict_price(coin_id, coin_data)
            
            # عرض النتائج
            print(f"\n📊 تحليل {coin_data['name']} ({coin_data['symbol']})")
            print(f"💰 السعر الحالي: ${coin_data['current_price']:,.6f}")
            print(f"📈 التغيير 24 ساعة: {coin_data['price_change_percentage_24h']:.2f}%")
            
            if technical_analysis:
                recommendation = technical_analysis.get('recommendation', 'غير محدد')
                print(f"🎯 التوصية: {recommendation}")
                
                strength_score = technical_analysis.get('strength_score', {})
                if strength_score:
                    print(f"💪 قوة الإشارة: {strength_score.get('percentage', 0):.1f}%")
            
            if prediction and 'predictions' in prediction:
                ensemble = prediction['predictions'].get('ensemble', {})
                if '7_day' in ensemble:
                    pred_7d = ensemble['7_day']
                    print(f"🔮 توقع 7 أيام: {pred_7d['direction']} ({pred_7d['price_change_percent']:+.2f}%)")
        
        input("\nاضغط Enter للمتابعة...")
        
    except Exception as e:
        print(f"❌ خطأ في التحليل: {e}")

async def show_portfolio_summary():
    """عرض ملخص المحفظة"""
    print("💼 ملخص المحفظة")
    
    try:
        from portfolio_manager import PortfolioManager
        
        portfolio_manager = PortfolioManager()
        
        # محاولة تحميل محفظة محفوظة
        portfolio_files = list(Path("data/portfolio").glob("*.json"))
        
        if portfolio_files:
            latest_file = max(portfolio_files, key=lambda x: x.stat().st_mtime)
            portfolio_manager.load_portfolio(str(latest_file))
            print(f"📂 تم تحميل المحفظة من: {latest_file.name}")
        
        summary = portfolio_manager.get_portfolio_summary()
        
        print(f"\n💰 إجمالي القيمة: ${summary['total_value']:,.2f}")
        print(f"💵 الرصيد النقدي: ${summary['cash_balance']:,.2f}")
        print(f"📊 المبلغ المستثمر: ${summary['invested_amount']:,.2f}")
        print(f"📈 الأرباح غير المحققة: ${summary['unrealized_pnl']:+,.2f}")
        print(f"💎 الأرباح المحققة: ${summary['realized_pnl']:+,.2f}")
        print(f"🎯 إجمالي الأرباح: ${summary['total_pnl']:+,.2f}")
        print(f"📍 عدد المراكز: {summary['positions_count']}")
        
        if summary['positions']:
            print("\n📋 المراكز النشطة:")
            for coin_id, position in summary['positions'].items():
                print(f"  • {coin_id.upper()}: {position['quantity']:.6f} "
                      f"(${position['market_value']:,.2f}, "
                      f"{position['pnl_percent']:+.2f}%)")
        
        input("\nاضغط Enter للمتابعة...")
        
    except Exception as e:
        print(f"❌ خطأ في عرض المحفظة: {e}")

async def update_strategies():
    """تحديث الاستراتيجيات"""
    print("⚡ تحديث الاستراتيجيات")
    
    try:
        from strategy_builder import StrategyBuilder
        from data_fetcher import CryptoDataFetcher
        from technical_analyzer import TechnicalAnalyzer
        
        coins = ['bitcoin', 'ethereum', 'solana']
        
        async with CryptoDataFetcher() as fetcher:
            for coin_id in coins:
                print(f"🔄 تحديث استراتيجية {coin_id}...")
                
                # جلب البيانات
                market_data = await fetcher.get_market_data([coin_id])
                
                if coin_id in market_data:
                    # التحليل الفني
                    analyzer = TechnicalAnalyzer()
                    technical_analysis = await analyzer.analyze(coin_id, market_data[coin_id])
                    
                    # بناء الاستراتيجية
                    strategy_builder = StrategyBuilder()
                    analysis_data = {
                        'market_data': market_data[coin_id],
                        'technical_analysis': technical_analysis
                    }
                    
                    strategy = await strategy_builder.build_strategy(coin_id, analysis_data)
                    
                    if strategy:
                        # حفظ الاستراتيجية
                        strategy_builder.save_strategy(strategy)
                        print(f"✅ تم تحديث استراتيجية {coin_id}")
                        
                        strategy_type = strategy.get('strategy_name', 'غير محدد')
                        evaluation = strategy.get('evaluation', {})
                        score = evaluation.get('score', 0)
                        
                        print(f"   📊 نوع الاستراتيجية: {strategy_type}")
                        print(f"   🎯 النتيجة: {score}/100")
                    else:
                        print(f"❌ فشل في بناء استراتيجية {coin_id}")
        
        print("\n✅ تم تحديث جميع الاستراتيجيات")
        input("اضغط Enter للمتابعة...")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث الاستراتيجيات: {e}")

async def analyze_news():
    """تحليل الأخبار"""
    print("📰 تحليل الأخبار")
    
    try:
        from news_analyzer import NewsAnalyzer
        
        async with NewsAnalyzer() as analyzer:
            print("🔍 جاري جلب الأخبار الحديثة...")
            
            # جلب الأخبار
            news_items = await analyzer.fetch_crypto_news(hours_back=24)
            
            if news_items:
                print(f"📰 تم جلب {len(news_items)} خبر")
                
                # تحليل المشاعر العام
                sentiment_summary = await analyzer.get_market_sentiment_summary()
                
                print(f"\n📊 ملخص المشاعر:")
                print(f"   🎭 المشاعر العامة: {sentiment_summary.get('overall_sentiment', 'غير محدد')}")
                print(f"   📈 نقاط الثقة: {sentiment_summary.get('confidence', 0):.2f}")
                print(f"   📋 الأخبار المحللة: {sentiment_summary.get('news_analyzed', 0)}")
                
                # عرض أحدث الأخبار
                print(f"\n📰 أحدث الأخبار:")
                for i, news in enumerate(news_items[:5], 1):
                    print(f"   {i}. {news.get('title', 'بدون عنوان')}")
                    print(f"      📅 {news.get('source', 'مصدر غير معروف')} - "
                          f"{news.get('published_date', 'تاريخ غير معروف')}")
                
                # المواضيع الرائجة
                trending_topics = await analyzer.get_trending_topics()
                
                if trending_topics:
                    print(f"\n🔥 المواضيع الرائجة:")
                    for topic in trending_topics[:5]:
                        print(f"   • {topic['topic']} ({topic['frequency']} مرة) - "
                              f"{topic['sentiment_label']}")
            else:
                print("❌ لم يتم العثور على أخبار")
        
        input("\nاضغط Enter للمتابعة...")
        
    except Exception as e:
        print(f"❌ خطأ في تحليل الأخبار: {e}")

def show_settings():
    """عرض الإعدادات"""
    print("⚙️  إعدادات النظام")
    
    settings_menu = """
    1. عرض المجلدات والملفات
    2. فحص المتطلبات
    3. تنظيف الملفات المؤقتة
    4. إعادة تعيين البيانات
    5. عرض السجلات
    0. العودة للقائمة الرئيسية
    """
    
    print(settings_menu)
    
    try:
        choice = input("اختر خيار: ").strip()
        
        if choice == '1':
            print("\n📁 المجلدات والملفات:")
            for item in project_root.rglob('*'):
                if item.is_file():
                    size = item.stat().st_size
                    print(f"   📄 {item.relative_to(project_root)} ({size:,} بايت)")
                elif item.is_dir():
                    print(f"   📁 {item.relative_to(project_root)}/")
        
        elif choice == '2':
            print("\n🔍 فحص المتطلبات:")
            check_dependencies()
        
        elif choice == '3':
            print("\n🧹 تنظيف الملفات المؤقتة...")
            # حذف ملفات السجلات القديمة
            logs_dir = project_root / "logs"
            if logs_dir.exists():
                for log_file in logs_dir.glob("*.log"):
                    if log_file.stat().st_size > 10 * 1024 * 1024:  # أكبر من 10 ميجا
                        log_file.unlink()
                        print(f"   🗑️  تم حذف {log_file.name}")
            print("✅ تم التنظيف")
        
        elif choice == '4':
            confirm = input("⚠️  هل أنت متأكد من إعادة تعيين جميع البيانات؟ (yes/no): ")
            if confirm.lower() == 'yes':
                # حذف ملفات البيانات
                data_dir = project_root / "data"
                if data_dir.exists():
                    import shutil
                    shutil.rmtree(data_dir)
                    create_directories()
                    print("✅ تم إعادة تعيين البيانات")
            else:
                print("❌ تم إلغاء العملية")
        
        elif choice == '5':
            print("\n📋 آخر 20 سطر من السجلات:")
            log_file = project_root / "logs" / "crypto_analyzer.log"
            if log_file.exists():
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    for line in lines[-20:]:
                        print(f"   {line.strip()}")
            else:
                print("   📄 لا توجد سجلات")
        
        input("\nاضغط Enter للمتابعة...")
        
    except Exception as e:
        print(f"❌ خطأ في الإعدادات: {e}")

def show_help():
    """عرض المساعدة"""
    help_text = """
    📚 مساعدة أداة تحليل العملات الرقمية
    
    🎯 الهدف:
    هذه الأداة تقوم بمراقبة وتحليل العملات الرقمية وتوليد التنبؤات
    وبناء استراتيجيات التداول التلقائية.
    
    🔧 المكونات الرئيسية:
    • جلب البيانات: من APIs مختلفة للعملات الرقمية
    • التحليل الفني: مؤشرات فنية متقدمة
    • تحليل الأخبار: تحليل المشاعر والتأثير على الأسعار
    • التنبؤ: نماذج ذكية للتنبؤ بالأسعار
    • الاستراتيجيات: بناء استراتيجيات تداول مخصصة
    • إدارة المحفظة: متابعة وإدارة الاستثمارات
    
    🚀 كيفية الاستخدام:
    1. ابدأ بالتشغيل الكامل للمراقبة المستمرة
    2. أو استخدم الوظائف المنفردة حسب الحاجة
    3. راقب لوحة التحكم للحصول على التحديثات
    
    ⚠️  ملاحظات مهمة:
    • تأكد من اتصال الإنترنت للحصول على البيانات
    • البيانات للأغراض التعليمية فقط
    • لا تعتمد على التنبؤات في اتخاذ قرارات استثمارية حقيقية
    
    🆘 الدعم:
    في حالة وجود مشاكل، تحقق من:
    • ملف السجلات في مجلد logs/
    • متطلبات النظام في requirements.txt
    • اتصال الإنترنت
    """
    
    print(help_text)
    input("\nاضغط Enter للمتابعة...")

async def main():
    """الدالة الرئيسية"""
    # إعداد النظام
    setup_logging()
    create_directories()
    
    # طباعة الشعار
    print_banner()
    
    # فحص المتطلبات
    if not check_dependencies():
        print("\n⚠️  يرجى تثبيت المتطلبات المفقودة أولاً")
        print("pip install -r requirements.txt")
        return
    
    # القائمة الرئيسية
    while True:
        try:
            print_menu()
            choice = input("اختر خيار (0-8): ").strip()
            
            if choice == '0':
                print("👋 شكراً لاستخدام أداة تحليل العملات الرقمية!")
                break
            
            elif choice == '1':
                await run_full_analysis()
            
            elif choice == '2':
                await run_dashboard_only()
            
            elif choice == '3':
                await analyze_specific_coin()
            
            elif choice == '4':
                await show_portfolio_summary()
            
            elif choice == '5':
                await update_strategies()
            
            elif choice == '6':
                await analyze_news()
            
            elif choice == '7':
                show_settings()
            
            elif choice == '8':
                show_help()
            
            else:
                print("❌ خيار غير صحيح، يرجى المحاولة مرة أخرى")
        
        except KeyboardInterrupt:
            print("\n👋 تم إنهاء البرنامج")
            break
        
        except Exception as e:
            print(f"❌ خطأ غير متوقع: {e}")
            logging.exception("خطأ في البرنامج الرئيسي")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء البرنامج")
    except Exception as e:
        print(f"❌ خطأ في تشغيل البرنامج: {e}")