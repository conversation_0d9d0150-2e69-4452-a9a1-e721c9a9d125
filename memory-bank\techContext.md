# السياق التقني

**التقنيات المستخدمة:**
-   **لغة البرمجة:** Python (بناءً على امتدادات الملفات `.py` الموجودة).
-   **إدارة الحزم:** pip (بناءً على وجود `requirements.txt`).
-   **جلب البيانات:** (متوقع) مكتبات مثل `requests` أو `websocket-client` للتفاعل مع واجهات برمجة تطبيقات البورصات.
-   **تحليل البيانات:** (متوقع) `pandas` لمعالجة البيانات، `numpy` للعمليات العددية.
-   **التعلم الآلي/التنبؤ:** (متوقع) `scikit-learn`, `tensorflow`, أو `pytorch` لبناء نماذج التنبؤ.
-   **تحليل الأخبار/معالجة اللغة الطبيعية (NLP):** (متوقع) `nltk`, `spaCy`, أو `TextBlob`.
-   **واجهة المستخدم (UI):** (متوقع) `Flask` أو `Django` كإطار عمل ويب، و `HTML`, `CSS`, `JavaScript` للواجهة الأمامية. قد تستخدم مكتبات رسوم بيانية مثل `Plotly` أو `Matplotlib`.
-   **قاعدة البيانات:** (متوقع) `SQLite` (بناءً على وجود `simple_crypto_analyzer/crypto_data.db`) أو PostgreSQL/MySQL لمشاريع أكبر.

**بيئة التطوير:**
-   **نظام التشغيل:** Windows (بناءً على وجود ملفات `.bat` مثل `start.bat`, `RUN.bat`, `تشغيل_الأداة.bat`).
-   **محرر الأكواد:** VS Code (بناءً على السياق المقدم).

**التبعيات (من `requirements.txt`):**

**`crypto_analyzer/requirements.txt`:**
-   pandas>=1.5.0
-   numpy>=1.21.0
-   scikit-learn>=1.1.0
-   talib>=0.4.25
-   aiohttp>=3.8.0
-   requests>=2.28.0
-   feedparser>=6.0.10
-   textblob>=0.17.1
-   beautifulsoup4>=4.11.0
-   flask>=2.2.0
-   flask-socketio>=5.3.0
-   plotly>=5.11.0
-   dash>=2.6.0
-   dash-bootstrap-components>=1.2.0
-   ta>=0.10.0
-   scipy>=1.9.0
-   tensorflow>=2.10.0
-   keras>=2.10.0
-   vaderSentiment>=3.3.0
-   nltk>=3.7
-   jinja2>=3.1.0
-   colorama>=0.4.0
-   tqdm>=4.64.0
-   python-dotenv>=0.19.0
-   asyncio-throttle>=1.0.0
-   joblib>=1.2.0
-   python-dateutil>=2.8.0
-   pytz>=2022.1
-   pytest>=7.0.0 (اختياري)
-   pytest-asyncio>=0.21.0 (اختياري)

**`simple_crypto_analyzer/requirements.txt`:**
-   streamlit>=1.28.0
-   pandas>=1.5.0
-   numpy>=1.21.0
-   plotly>=5.11.0
-   requests>=2.28.0
-   python-dateutil>=2.8.0
-   sqlite3 (مدمجة في Python)

**القيود التقنية:**
-   **معدل واجهة برمجة التطبيقات (API Rate Limits):** يجب مراعاة حدود معدل الطلبات عند جلب البيانات من البورصات.
-   **أداء نماذج التعلم الآلي:** قد تتطلب النماذج المعقدة موارد حوسبة كبيرة.
-   **تحديث البيانات في الوقت الفعلي:** يتطلب تصميمًا فعالًا للتعامل مع تدفق البيانات المستمر.

**أدوات إضافية (MCP Integrations):**
-   يبدو أن هناك ملف `crypto_analyzer/mcp_integrations.py`، مما يشير إلى وجود تكاملات مع بروتوكول سياق النموذج (MCP) لأدوات أو موارد إضافية.
