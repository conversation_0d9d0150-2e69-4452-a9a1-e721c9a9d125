"""
أداة تحليل العملات الرقمية المتقدمة
Advanced Cryptocurrency Analysis Tool
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional
import json

from data_fetcher import CryptoDataFetcher
from technical_analyzer import TechnicalAnalyzer
from news_analyzer import NewsAnalyzer
from prediction_engine import PredictionEngine
from strategy_builder import StrategyBuilder
from portfolio_manager import PortfolioManager
from ui.dashboard import Dashboard
from scalping_engine import ScalpingEngine
from mcp_integrations import MCPIntegrations

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('crypto_analyzer.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class CryptoAnalyzer:
    """الفئة الرئيسية لأداة تحليل العملات الرقمية"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.data_fetcher = CryptoDataFetcher()
        self.technical_analyzer = TechnicalAnalyzer()
        self.news_analyzer = NewsAnalyzer()
        self.prediction_engine = PredictionEngine()
        self.strategy_builder = StrategyBuilder()
        self.portfolio_manager = PortfolioManager()
        self.dashboard = Dashboard()
        self.scalping_engine = ScalpingEngine()
        self.mcp_integrations = MCPIntegrations()
        
        # قائمة العملات المراقبة
        self.watched_coins = [
            'bitcoin', 'ethereum', 'binancecoin', 'cardano', 'solana',
            'polkadot', 'dogecoin', 'avalanche-2', 'chainlink', 'polygon'
        ]
        
        self.is_running = False
        
    async def start_monitoring(self):
        """بدء مراقبة العملات الرقمية"""
        self.logger.info("🚀 بدء تشغيل أداة تحليل العملات الرقمية")
        self.is_running = True
        
        # تشغيل المهام المتوازية
        tasks = [
            self.monitor_prices(),
            self.analyze_news(),
            self.generate_predictions(),
            self.update_strategies(),
            self.run_dashboard()
        ]
        
        await asyncio.gather(*tasks)
    
    async def monitor_prices(self):
        """مراقبة أسعار العملات الرقمية"""
        while self.is_running:
            try:
                # جلب البيانات الحالية
                market_data = await self.data_fetcher.get_market_data(self.watched_coins)
                
                for coin_id, data in market_data.items():
                    # التحليل الفني
                    technical_analysis = await self.technical_analyzer.analyze(coin_id, data)
                    
                    # التنبؤ بالسعر
                    prediction = await self.prediction_engine.predict_price(coin_id, data)
                    
                    # تحديث البيانات
                    await self.update_coin_analysis(coin_id, {
                        'market_data': data,
                        'technical_analysis': technical_analysis,
                        'prediction': prediction,
                        'timestamp': datetime.now()
                    })
                
                self.logger.info(f"✅ تم تحديث بيانات {len(market_data)} عملة رقمية")
                
            except Exception as e:
                self.logger.error(f"❌ خطأ في مراقبة الأسعار: {e}")
            
            # انتظار 30 ثانية قبل التحديث التالي
            await asyncio.sleep(30)
    
    async def analyze_news(self):
        """تحليل الأخبار وتأثيرها على العملات"""
        while self.is_running:
            try:
                # جلب الأخبار الحديثة
                news_data = await self.news_analyzer.fetch_crypto_news()
                
                for news_item in news_data:
                    # تحليل المشاعر
                    sentiment = await self.news_analyzer.analyze_sentiment(news_item['content'])
                    
                    # تحديد العملات المتأثرة
                    affected_coins = await self.news_analyzer.identify_affected_coins(news_item)
                    
                    # حفظ تحليل الأخبار
                    await self.save_news_analysis({
                        'news': news_item,
                        'sentiment': sentiment,
                        'affected_coins': affected_coins,
                        'impact_score': sentiment['compound'] * news_item.get('importance', 1.0)
                    })
                
                self.logger.info(f"📰 تم تحليل {len(news_data)} خبر جديد")
                
            except Exception as e:
                self.logger.error(f"❌ خطأ في تحليل الأخبار: {e}")
            
            # انتظار 5 دقائق قبل جلب الأخبار مرة أخرى
            await asyncio.sleep(300)
    
    async def generate_predictions(self):
        """توليد التنبؤات والتوصيات"""
        while self.is_running:
            try:
                for coin_id in self.watched_coins:
                    # الحصول على البيانات التاريخية
                    historical_data = await self.data_fetcher.get_historical_data(coin_id, days=30)
                    
                    # توليد التنبؤات
                    predictions = await self.prediction_engine.generate_comprehensive_prediction(
                        coin_id, historical_data
                    )
                    
                    # حفظ التنبؤات
                    await self.save_predictions(coin_id, predictions)
                
                self.logger.info("🔮 تم توليد التنبؤات لجميع العملات")
                
            except Exception as e:
                self.logger.error(f"❌ خطأ في توليد التنبؤات: {e}")
            
            # انتظار 15 دقيقة قبل توليد تنبؤات جديدة
            await asyncio.sleep(900)
    
    async def update_strategies(self):
        """تحديث استراتيجيات التداول"""
        while self.is_running:
            try:
                for coin_id in self.watched_coins:
                    # الحصول على التحليل الحالي
                    current_analysis = await self.get_current_analysis(coin_id)
                    
                    if current_analysis:
                        # بناء الاستراتيجية
                        strategy = await self.strategy_builder.build_strategy(
                            coin_id, current_analysis
                        )
                        
                        # تحديث المحفظة
                        await self.portfolio_manager.update_strategy(coin_id, strategy)
                
                self.logger.info("📈 تم تحديث استراتيجيات التداول")
                
            except Exception as e:
                self.logger.error(f"❌ خطأ في تحديث الاستراتيجيات: {e}")
            
            # انتظار 10 دقائق قبل تحديث الاستراتيجيات
            await asyncio.sleep(600)
    
    async def run_dashboard(self):
        """تشغيل لوحة التحكم"""
        try:
            await self.dashboard.start_server()
        except Exception as e:
            self.logger.error(f"❌ خطأ في تشغيل لوحة التحكم: {e}")
    
    async def update_coin_analysis(self, coin_id: str, analysis_data: Dict):
        """تحديث تحليل العملة"""
        # حفظ البيانات في قاعدة البيانات أو ملف
        filename = f"data/analysis/{coin_id}_analysis.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(analysis_data, f, ensure_ascii=False, indent=2, default=str)
        except Exception as e:
            self.logger.error(f"خطأ في حفظ تحليل {coin_id}: {e}")
    
    async def save_news_analysis(self, news_analysis: Dict):
        """حفظ تحليل الأخبار"""
        filename = f"data/news/news_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(news_analysis, f, ensure_ascii=False, indent=2, default=str)
        except Exception as e:
            self.logger.error(f"خطأ في حفظ تحليل الأخبار: {e}")
    
    async def save_predictions(self, coin_id: str, predictions: Dict):
        """حفظ التنبؤات"""
        filename = f"data/predictions/{coin_id}_predictions.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(predictions, f, ensure_ascii=False, indent=2, default=str)
        except Exception as e:
            self.logger.error(f"خطأ في حفظ تنبؤات {coin_id}: {e}")
    
    async def get_current_analysis(self, coin_id: str) -> Optional[Dict]:
        """الحصول على التحليل الحالي للعملة"""
        filename = f"data/analysis/{coin_id}_analysis.json"
        
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            return None
        except Exception as e:
            self.logger.error(f"خطأ في قراءة تحليل {coin_id}: {e}")
            return None
    
    def stop_monitoring(self):
        """إيقاف المراقبة"""
        self.logger.info("⏹️ إيقاف أداة تحليل العملات الرقمية")
        self.is_running = False

async def main():
    """الدالة الرئيسية"""
    analyzer = CryptoAnalyzer()
    
    try:
        await analyzer.start_monitoring()
    except KeyboardInterrupt:
        analyzer.stop_monitoring()
        print("\n👋 تم إيقاف البرنامج بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل البرنامج: {e}")

if __name__ == "__main__":
    # إنشاء المجلدات المطلوبة
    import os
    os.makedirs("data/analysis", exist_ok=True)
    os.makedirs("data/news", exist_ok=True)
    os.makedirs("data/predictions", exist_ok=True)
    os.makedirs("data/strategies", exist_ok=True)
    
    # تشغيل البرنامج
    asyncio.run(main())