# أنماط النظام

**الهندسة المعمارية العامة:**
يعتمد المشروع على بنية معيارية (Modular Architecture) حيث يتم تقسيم الوظائف إلى وحدات مستقلة (مثل جلب البيانات، تحليل الأخبار، محرك التنبؤ، إلخ). هذا يسهل التطوير، الصيانة، وإعادة الاستخدام.

**المكونات الرئيسية والعلاقات:**
```mermaid
graph TD
    A[واجهة المستخدم (UI)] --> B(جلب البيانات)
    A --> C(محلل الأخبار)
    A --> D(محرك التنبؤ)
    A --> E(باني الاستراتيجيات)
    A --> F(مدير المحافظ)
    B --> G[قاعدة البيانات/التخزين]
    C --> G
    D --> G
    E --> G
    F --> G
    B --> H(واجهات برمجة تطبيقات البورصات)
    C --> I(مصادر الأخبار/واجهات برمجة التطبيقات)
    D --> J(نماذج التعلم الآلي)
    E --> K(محرك الاختبار الخلفي)
    F --> L(واجهات برمجة تطبيقات المحافظ)
```

**أنماط التصميم المستخدمة (المتوقعة):**
-   **نمط الوحدة (Module Pattern):** كل مكون رئيسي (مثل `data_fetcher.py`, `news_analyzer.py`) يعمل كوحدة مستقلة ذات مسؤوليات محددة.
-   **نمط المستودع (Repository Pattern):** (متوقع) لفصل منطق الوصول إلى البيانات عن منطق الأعمال، مما يسهل التبديل بين مصادر البيانات المختلفة.
-   **نمط المراقب (Observer Pattern):** (محتمل) لتحديث واجهة المستخدم تلقائيًا عند تغيير البيانات أو حدوث أحداث معينة (مثل تنبيهات التداول).
-   **نمط المصنع (Factory Pattern):** (محتمل) لإنشاء كائنات معقدة مثل نماذج التنبؤ أو استراتيجيات التداول.

**قرارات التصميم الرئيسية:**
-   **فصل الاهتمامات (Separation of Concerns):** كل وحدة تركز على مهمة واحدة محددة.
-   **قابلية التوسع (Scalability):** تصميم يسمح بإضافة المزيد من مصادر البيانات، نماذج التنبؤ، أو استراتيجيات التداول بسهولة.
-   **قابلية الاختبار (Testability):** الوحدات المستقلة تسهل كتابة الاختبارات لكل مكون على حدة.

**تدفق البيانات (مثال: جلب البيانات وعرضها):**
1.  تطلب واجهة المستخدم بيانات معينة (مثل سعر البيتكوين الحالي).
2.  يقوم مكون "جلب البيانات" بالاتصال بواجهة برمجة تطبيقات البورصة.
3.  يتم جلب البيانات وتخزينها مؤقتًا أو بشكل دائم في قاعدة البيانات.
4.  يتم إرجاع البيانات إلى واجهة المستخدم لعرضها.
