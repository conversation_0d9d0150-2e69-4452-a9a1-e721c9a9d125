"""
ملف التكوين الرئيسي لأداة تحليل العملات الرقمية
Main Configuration File for Cryptocurrency Analysis Tool
"""

import os
from pathlib import Path

# مسارات المشروع
PROJECT_ROOT = Path(__file__).parent
DATA_DIR = PROJECT_ROOT / "data"
LOGS_DIR = PROJECT_ROOT / "logs"
UI_DIR = PROJECT_ROOT / "ui"

# إعدادات قاعدة البيانات والملفات
PATHS = {
    'data': DATA_DIR,
    'analysis': DATA_DIR / "analysis",
    'news': DATA_DIR / "news",
    'predictions': DATA_DIR / "predictions",
    'strategies': DATA_DIR / "strategies",
    'portfolio': DATA_DIR / "portfolio",
    'logs': LOGS_DIR,
    'ui_templates': UI_DIR / "templates",
    'ui_static': UI_DIR / "static"
}

# إعدادات APIs
API_SETTINGS = {
    'coingecko': {
        'base_url': 'https://api.coingecko.com/api/v3',
        'rate_limit': 50,  # requests per minute
        'timeout': 30
    },
    'coinpaprika': {
        'base_url': 'https://api.coinpaprika.com/v1',
        'rate_limit': 25000,
        'timeout': 30
    },
    'binance': {
        'base_url': 'https://api.binance.com/api/v3',
        'rate_limit': 1200,
        'timeout': 30
    },
    'newsapi': {
        'base_url': 'https://newsapi.org/v2',
        'api_key': os.getenv('NEWS_API_KEY', ''),
        'rate_limit': 1000,
        'timeout': 30
    },
    'fear_greed': {
        'base_url': 'https://api.alternative.me/fng/',
        'timeout': 30
    }
}

# العملات المراقبة الافتراضية
DEFAULT_WATCHED_COINS = [
    'bitcoin',
    'ethereum', 
    'binancecoin',
    'cardano',
    'solana',
    'polkadot',
    'dogecoin',
    'avalanche-2',
    'chainlink',
    'polygon'
]

# إعدادات التحديث (بالثواني)
UPDATE_INTERVALS = {
    'price_monitoring': 30,      # مراقبة الأسعار
    'news_analysis': 300,        # تحليل الأخبار (5 دقائق)
    'predictions': 900,          # التنبؤات (15 دقيقة)
    'strategies': 600,           # الاستراتيجيات (10 دقائق)
    'portfolio_update': 60,      # تحديث المحفظة (دقيقة)
    'dashboard_refresh': 10      # تحديث لوحة التحكم (10 ثواني)
}

# إعدادات التحليل الفني
TECHNICAL_ANALYSIS_CONFIG = {
    'sma_periods': [5, 10, 20, 50, 100, 200],
    'ema_periods': [12, 26, 50, 100],
    'rsi_period': 14,
    'macd_fast': 12,
    'macd_slow': 26,
    'macd_signal': 9,
    'bb_period': 20,
    'bb_std': 2,
    'stoch_k': 14,
    'stoch_d': 3,
    'adx_period': 14,
    'cci_period': 20,
    'williams_r_period': 14,
    'atr_period': 14,
    'volume_sma_period': 20
}

# إعدادات التنبؤ
PREDICTION_CONFIG = {
    'lookback_days': 30,
    'prediction_periods': [1, 3, 7, 14, 30],  # أيام
    'models': {
        'random_forest': {
            'n_estimators': 100,
            'random_state': 42,
            'max_depth': 10
        },
        'gradient_boosting': {
            'n_estimators': 100,
            'random_state': 42,
            'learning_rate': 0.1
        },
        'linear_regression': {
            'fit_intercept': True
        }
    },
    'train_test_split': 0.8,
    'feature_scaling': 'standard'  # 'standard' or 'minmax'
}

# إعدادات إدارة المخاطر
RISK_MANAGEMENT = {
    'max_position_size': 0.2,        # 20% من المحفظة
    'max_sector_allocation': 0.4,     # 40% لقطاع واحد
    'stop_loss_threshold': 0.15,      # 15% وقف خسارة
    'take_profit_threshold': 0.3,     # 30% جني أرباح
    'max_daily_loss': 0.02,          # 2% خسارة يومية قصوى
    'risk_per_trade': 0.01,          # 1% مخاطرة لكل صفقة
    'correlation_limit': 0.7,         # حد الارتباط بين الأصول
    'volatility_limit': 0.5,          # حد التقلبات
    'max_positions': 10               # أقصى عدد مراكز
}

# إعدادات المحفظة الافتراضية
PORTFOLIO_CONFIG = {
    'initial_balance': 10000,         # الرصيد الأولي (دولار)
    'currency': 'USD',                # العملة الأساسية
    'rebalance_frequency': 7,         # إعادة التوازن (أيام)
    'risk_tolerance': 'متوسط',       # منخفض، متوسط، عالي
    'investment_horizon': 'متوسط المدى',  # قصير، متوسط، طويل
    'auto_rebalance': True,           # إعادة التوازن التلقائية
    'compound_returns': True          # إعادة استثمار الأرباح
}

# إعدادات الاستراتيجيات
STRATEGY_CONFIG = {
    'available_strategies': [
        'trend_following',
        'mean_reversion', 
        'momentum',
        'breakout',
        'scalping',
        'swing_trading',
        'dca',
        'grid_trading'
    ],
    'evaluation_criteria': {
        'profitability': 0.3,
        'risk_adjusted_return': 0.25,
        'win_rate': 0.2,
        'max_drawdown': 0.15,
        'volatility': 0.1
    },
    'auto_strategy_selection': True,
    'strategy_update_frequency': 24   # ساعات
}

# إعدادات تحليل الأخبار
NEWS_ANALYSIS_CONFIG = {
    'sources': {
        'coindesk': {
            'rss': 'https://www.coindesk.com/arc/outboundfeeds/rss/',
            'weight': 0.9
        },
        'cointelegraph': {
            'rss': 'https://cointelegraph.com/rss',
            'weight': 0.8
        },
        'cryptonews': {
            'rss': 'https://cryptonews.com/news/feed',
            'weight': 0.7
        },
        'decrypt': {
            'rss': 'https://decrypt.co/feed',
            'weight': 0.8
        }
    },
    'sentiment_keywords': {
        'positive': [
            'bullish', 'surge', 'rally', 'pump', 'moon', 'adoption',
            'breakthrough', 'partnership', 'upgrade', 'launch',
            'positive', 'growth', 'increase', 'rise', 'gain',
            'breakthrough', 'innovation', 'success'
        ],
        'negative': [
            'bearish', 'crash', 'dump', 'fall', 'decline', 'drop',
            'hack', 'scam', 'regulation', 'ban', 'negative',
            'loss', 'decrease', 'plunge', 'collapse', 'fear',
            'uncertainty', 'risk', 'warning'
        ]
    },
    'max_news_age_hours': 24,
    'min_relevance_score': 0.3,
    'language': 'en'
}

# إعدادات لوحة التحكم
DASHBOARD_CONFIG = {
    'host': '0.0.0.0',
    'port': 5000,
    'debug': False,
    'auto_open_browser': True,
    'update_interval': 30,            # ثواني
    'max_alerts': 50,
    'theme': 'light',                 # light, dark
    'language': 'ar',                 # ar, en
    'timezone': 'Africa/Cairo'
}

# إعدادات السجلات
LOGGING_CONFIG = {
    'level': 'INFO',                  # DEBUG, INFO, WARNING, ERROR
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'max_file_size': 10 * 1024 * 1024,  # 10 MB
    'backup_count': 5,
    'console_output': True,
    'file_output': True,
    'encoding': 'utf-8'
}

# إعدادات الأداء
PERFORMANCE_CONFIG = {
    'max_concurrent_requests': 10,
    'request_timeout': 30,
    'retry_attempts': 3,
    'retry_delay': 1,                 # ثواني
    'cache_duration': 300,            # ثواني (5 دقائق)
    'max_memory_usage': 1024,         # MB
    'cleanup_interval': 3600          # ثواني (ساعة)
}

# إعدادات التنبيهات
ALERT_CONFIG = {
    'price_change_threshold': 0.05,   # 5% تغيير في السعر
    'volume_spike_threshold': 2.0,    # ضعف الحجم العادي
    'news_sentiment_threshold': 0.7,  # قوة المشاعر
    'strategy_performance_threshold': -0.1,  # -10% أداء الاستراتيجية
    'portfolio_loss_threshold': -0.05,  # -5% خسارة المحفظة
    'enabled_alerts': [
        'price_alerts',
        'volume_alerts', 
        'news_alerts',
        'strategy_alerts',
        'portfolio_alerts'
    ]
}

# إعدادات النسخ الاحتياطي
BACKUP_CONFIG = {
    'auto_backup': True,
    'backup_interval': 24,            # ساعات
    'max_backups': 7,                 # عدد النسخ المحفوظة
    'backup_location': DATA_DIR / "backups",
    'compress_backups': True
}

# إعدادات الأمان
SECURITY_CONFIG = {
    'encrypt_sensitive_data': False,   # تشفير البيانات الحساسة
    'api_key_encryption': False,       # تشفير مفاتيح API
    'session_timeout': 3600,           # انتهاء الجلسة (ثواني)
    'max_login_attempts': 5,
    'lockout_duration': 300            # مدة الحظر (ثواني)
}

# إعدادات التطوير والاختبار
DEVELOPMENT_CONFIG = {
    'debug_mode': False,
    'test_mode': False,
    'mock_api_responses': False,
    'verbose_logging': False,
    'profiling_enabled': False,
    'test_data_path': DATA_DIR / "test_data"
}

# دالة للحصول على الإعدادات
def get_config(section: str = None):
    """
    الحصول على إعدادات محددة أو جميع الإعدادات
    
    Args:
        section: اسم القسم (اختياري)
    
    Returns:
        dict: الإعدادات المطلوبة
    """
    all_config = {
        'paths': PATHS,
        'api_settings': API_SETTINGS,
        'watched_coins': DEFAULT_WATCHED_COINS,
        'update_intervals': UPDATE_INTERVALS,
        'technical_analysis': TECHNICAL_ANALYSIS_CONFIG,
        'prediction': PREDICTION_CONFIG,
        'risk_management': RISK_MANAGEMENT,
        'portfolio': PORTFOLIO_CONFIG,
        'strategy': STRATEGY_CONFIG,
        'news_analysis': NEWS_ANALYSIS_CONFIG,
        'dashboard': DASHBOARD_CONFIG,
        'logging': LOGGING_CONFIG,
        'performance': PERFORMANCE_CONFIG,
        'alerts': ALERT_CONFIG,
        'backup': BACKUP_CONFIG,
        'security': SECURITY_CONFIG,
        'development': DEVELOPMENT_CONFIG
    }
    
    if section:
        return all_config.get(section, {})
    
    return all_config

# دالة لتحديث الإعدادات
def update_config(section: str, key: str, value):
    """
    تحديث إعداد محدد
    
    Args:
        section: اسم القسم
        key: مفتاح الإعداد
        value: القيمة الجديدة
    """
    config_map = {
        'api_settings': API_SETTINGS,
        'update_intervals': UPDATE_INTERVALS,
        'technical_analysis': TECHNICAL_ANALYSIS_CONFIG,
        'prediction': PREDICTION_CONFIG,
        'risk_management': RISK_MANAGEMENT,
        'portfolio': PORTFOLIO_CONFIG,
        'strategy': STRATEGY_CONFIG,
        'news_analysis': NEWS_ANALYSIS_CONFIG,
        'dashboard': DASHBOARD_CONFIG,
        'logging': LOGGING_CONFIG,
        'performance': PERFORMANCE_CONFIG,
        'alerts': ALERT_CONFIG,
        'backup': BACKUP_CONFIG,
        'security': SECURITY_CONFIG,
        'development': DEVELOPMENT_CONFIG
    }
    
    if section in config_map:
        config_map[section][key] = value
        return True
    
    return False

# دالة لإنشاء المجلدات المطلوبة
def create_required_directories():
    """إنشاء جميع المجلدات المطلوبة"""
    for path in PATHS.values():
        path.mkdir(parents=True, exist_ok=True)

# دالة للتحقق من صحة الإعدادات
def validate_config():
    """التحقق من صحة الإعدادات"""
    errors = []
    
    # التحقق من المسارات
    try:
        create_required_directories()
    except Exception as e:
        errors.append(f"خطأ في إنشاء المجلدات: {e}")
    
    # التحقق من إعدادات المحفظة
    if PORTFOLIO_CONFIG['initial_balance'] <= 0:
        errors.append("الرصيد الأولي يجب أن يكون أكبر من صفر")
    
    # التحقق من إعدادات المخاطر
    if not (0 < RISK_MANAGEMENT['max_position_size'] <= 1):
        errors.append("حجم المركز الأقصى يجب أن يكون بين 0 و 1")
    
    # التحقق من فترات التحديث
    for interval in UPDATE_INTERVALS.values():
        if interval <= 0:
            errors.append("فترات التحديث يجب أن تكون أكبر من صفر")
    
    return errors

# تشغيل التحقق عند استيراد الملف
if __name__ == "__main__":
    errors = validate_config()
    if errors:
        print("أخطاء في التكوين:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("✅ جميع الإعدادات صحيحة")
        create_required_directories()
        print("✅ تم إنشاء جميع المجلدات المطلوبة")