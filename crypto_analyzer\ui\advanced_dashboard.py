"""
لوحة تحكم متقدمة مع الشارتات والبيانات التاريخية
Advanced Dashboard with Charts and Historical Data
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import pandas as pd
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px
from flask import Flask, render_template_string, jsonify, request
import threading
import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from data_fetcher import CryptoDataFetcher
from technical_analyzer import TechnicalAnalyzer
from prediction_engine import PredictionEngine
from mcp_integrations import MCPIntegrations

class AdvancedCryptoDashboard:
    """لوحة تحكم متقدمة للعملات الرقمية مع الشارتات"""
    
    def __init__(self, port: int = 5001):
        self.app = Flask(__name__)
        self.port = port
        self.logger = logging.getLogger(__name__)
        
        # بيانات مؤقتة للشارتات
        self.chart_data = {}
        self.market_data = {}
        self.technical_data = {}
        self.prediction_data = {}
        
        # العملات المدعومة
        self.supported_coins = [
            'bitcoin', 'ethereum', 'binancecoin', 'cardano', 'solana',
            'polkadot', 'dogecoin', 'avalanche-2', 'chainlink', 'polygon'
        ]
        
        # إعداد المسارات
        self.setup_routes()
        
        # بدء تحديث البيانات
        self.update_thread = None
        self.running = False
    
    def setup_routes(self):
        """إعداد مسارات Flask"""
        
        @self.app.route('/')
        def dashboard():
            """الصفحة الرئيسية للوحة التحكم"""
            return render_template_string(self.get_dashboard_template())
        
        @self.app.route('/api/market-data')
        def get_market_data():
            """الحصول على بيانات السوق"""
            return jsonify(self.market_data)
        
        @self.app.route('/api/chart-data/<coin_id>')
        def get_chart_data(coin_id):
            """الحصول على بيانات الشارت لعملة معينة"""
            timeframe = request.args.get('timeframe', '7d')
            return jsonify(self.chart_data.get(coin_id, {}))
        
        @self.app.route('/api/technical-analysis/<coin_id>')
        def get_technical_analysis(coin_id):
            """الحصول على التحليل الفني لعملة معينة"""
            return jsonify(self.technical_data.get(coin_id, {}))
        
        @self.app.route('/api/predictions/<coin_id>')
        def get_predictions(coin_id):
            """الحصول على التنبؤات لعملة معينة"""
            return jsonify(self.prediction_data.get(coin_id, {}))
        
        @self.app.route('/api/fear-greed')
        def get_fear_greed():
            """الحصول على مؤشر الخوف والطمع"""
            return jsonify(self.market_data.get('fear_greed', {}))
        
        @self.app.route('/api/generate-chart/<coin_id>')
        def generate_chart(coin_id):
            """توليد شارت تفاعلي لعملة معينة"""
            chart_html = self.generate_interactive_chart(coin_id)
            return chart_html
    
    def get_dashboard_template(self) -> str:
        """قالب HTML للوحة التحكم المتقدمة"""
        return """
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 لوحة تحكم العملات الرقمية المتقدمة</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: #fff;
        }
        .dashboard-container {
            padding: 20px;
        }
        .crypto-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }
        .crypto-card:hover {
            transform: translateY(-5px);
        }
        .price-positive {
            color: #00ff88;
        }
        .price-negative {
            color: #ff4757;
        }
        .chart-container {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
        }
        .fear-greed-meter {
            text-align: center;
            padding: 20px;
        }
        .fear-greed-value {
            font-size: 3em;
            font-weight: bold;
        }
        .trading-signals {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
        }
        .signal-buy {
            color: #00ff88;
            font-weight: bold;
        }
        .signal-sell {
            color: #ff4757;
            font-weight: bold;
        }
        .signal-hold {
            color: #ffa502;
            font-weight: bold;
        }
        .nav-tabs .nav-link {
            color: #fff;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-right: 5px;
        }
        .nav-tabs .nav-link.active {
            background: rgba(255, 255, 255, 0.2);
            color: #fff;
        }
        .table-dark {
            background: rgba(0, 0, 0, 0.3);
        }
        .loading {
            text-align: center;
            padding: 50px;
        }
        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 4px solid #fff;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .prediction-card {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
        }
        .technical-indicator {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
    </style>
</head>
<body>
    <div class="container-fluid dashboard-container">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="crypto-card text-center">
                    <h1><i class="fas fa-chart-line"></i> لوحة تحكم العملات الرقمية المتقدمة</h1>
                    <p class="mb-0">مراقبة وتحليل العملات الرقمية في الوقت الفعلي</p>
                    <small id="last-update">آخر تحديث: جاري التحميل...</small>
                </div>
            </div>
        </div>

        <!-- Fear & Greed Index -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="crypto-card fear-greed-meter">
                    <h4><i class="fas fa-thermometer-half"></i> مؤشر الخوف والطمع</h4>
                    <div class="fear-greed-value" id="fear-greed-value">--</div>
                    <div id="fear-greed-text">جاري التحميل...</div>
                </div>
            </div>
            <div class="col-md-8">
                <div class="crypto-card">
                    <h5><i class="fas fa-newspaper"></i> أحدث الأخبار</h5>
                    <div id="news-container">
                        <div class="loading">
                            <div class="spinner"></div>
                            <p>جاري تحميل الأخبار...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Market Overview -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="crypto-card">
                    <h4><i class="fas fa-coins"></i> نظرة عامة على السوق</h4>
                    <div class="table-responsive">
                        <table class="table table-dark table-hover">
                            <thead>
                                <tr>
                                    <th>العملة</th>
                                    <th>السعر</th>
                                    <th>التغيير 24س</th>
                                    <th>القيمة السوقية</th>
                                    <th>الحجم</th>
                                    <th>إشارة التداول</th>
                                </tr>
                            </thead>
                            <tbody id="market-table">
                                <tr>
                                    <td colspan="6" class="text-center">
                                        <div class="spinner"></div>
                                        جاري تحميل بيانات السوق...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="crypto-card">
                    <h4><i class="fas fa-chart-area"></i> الشارتات التفاعلية</h4>
                    
                    <!-- Coin Selection -->
                    <div class="mb-3">
                        <select class="form-select" id="coin-selector" onchange="loadCoinChart()">
                            <option value="bitcoin">Bitcoin (BTC)</option>
                            <option value="ethereum">Ethereum (ETH)</option>
                            <option value="binancecoin">Binance Coin (BNB)</option>
                            <option value="cardano">Cardano (ADA)</option>
                            <option value="solana">Solana (SOL)</option>
                        </select>
                    </div>
                    
                    <!-- Timeframe Selection -->
                    <div class="mb-3">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-light" onclick="changeTimeframe('1d')">1 يوم</button>
                            <button type="button" class="btn btn-outline-light active" onclick="changeTimeframe('7d')">7 أيام</button>
                            <button type="button" class="btn btn-outline-light" onclick="changeTimeframe('30d')">30 يوم</button>
                            <button type="button" class="btn btn-outline-light" onclick="changeTimeframe('90d')">90 يوم</button>
                        </div>
                    </div>
                    
                    <!-- Chart Container -->
                    <div class="chart-container">
                        <div id="price-chart" style="height: 400px;">
                            <div class="loading">
                                <div class="spinner"></div>
                                <p>جاري تحميل الشارت...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Technical Analysis & Predictions -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="crypto-card">
                    <h5><i class="fas fa-chart-line"></i> التحليل الفني</h5>
                    <div id="technical-analysis">
                        <div class="loading">
                            <div class="spinner"></div>
                            <p>جاري تحميل التحليل الفني...</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="crypto-card">
                    <h5><i class="fas fa-crystal-ball"></i> التنبؤات</h5>
                    <div id="predictions">
                        <div class="loading">
                            <div class="spinner"></div>
                            <p>جاري تحميل التنبؤات...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Trading Signals -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="crypto-card">
                    <h4><i class="fas fa-signal"></i> إشارات التداول</h4>
                    <div class="trading-signals" id="trading-signals">
                        <div class="loading">
                            <div class="spinner"></div>
                            <p>جاري تحميل إشارات التداول...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentCoin = 'bitcoin';
        let currentTimeframe = '7d';
        
        // تحديث البيانات كل 30 ثانية
        setInterval(updateDashboard, 30000);
        
        // تحميل البيانات عند بدء التشغيل
        window.onload = function() {
            updateDashboard();
        };
        
        async function updateDashboard() {
            try {
                await Promise.all([
                    updateMarketData(),
                    updateFearGreed(),
                    loadCoinChart(),
                    updateTechnicalAnalysis(),
                    updatePredictions()
                ]);
                
                document.getElementById('last-update').textContent = 
                    'آخر تحديث: ' + new Date().toLocaleString('ar-EG');
            } catch (error) {
                console.error('خطأ في تحديث البيانات:', error);
            }
        }
        
        async function updateMarketData() {
            try {
                const response = await fetch('/api/market-data');
                const data = await response.json();
                
                const tableBody = document.getElementById('market-table');
                tableBody.innerHTML = '';
                
                if (data.coins) {
                    data.coins.forEach(coin => {
                        const row = document.createElement('tr');
                        const changeClass = coin.price_change_percentage_24h >= 0 ? 'price-positive' : 'price-negative';
                        const changeIcon = coin.price_change_percentage_24h >= 0 ? '↗' : '↘';
                        
                        row.innerHTML = `
                            <td>
                                <img src="${coin.image}" width="24" height="24" class="me-2">
                                ${coin.name} (${coin.symbol.toUpperCase()})
                            </td>
                            <td>$${coin.current_price.toLocaleString()}</td>
                            <td class="${changeClass}">
                                ${changeIcon} ${coin.price_change_percentage_24h.toFixed(2)}%
                            </td>
                            <td>$${coin.market_cap.toLocaleString()}</td>
                            <td>$${coin.total_volume.toLocaleString()}</td>
                            <td><span class="signal-hold">انتظار</span></td>
                        `;
                        tableBody.appendChild(row);
                    });
                }
            } catch (error) {
                console.error('خطأ في تحديث بيانات السوق:', error);
            }
        }
        
        async function updateFearGreed() {
            try {
                const response = await fetch('/api/fear-greed');
                const data = await response.json();
                
                if (data.current) {
                    document.getElementById('fear-greed-value').textContent = data.current.value;
                    document.getElementById('fear-greed-text').textContent = data.current.interpretation;
                }
            } catch (error) {
                console.error('خطأ في تحديث مؤشر الخوف والطمع:', error);
            }
        }
        
        async function loadCoinChart() {
            try {
                const response = await fetch(`/api/generate-chart/${currentCoin}`);
                const chartHtml = await response.text();
                document.getElementById('price-chart').innerHTML = chartHtml;
            } catch (error) {
                console.error('خطأ في تحميل الشارت:', error);
                document.getElementById('price-chart').innerHTML = 
                    '<p class="text-center">خطأ في تحميل الشارت</p>';
            }
        }
        
        async function updateTechnicalAnalysis() {
            try {
                const response = await fetch(`/api/technical-analysis/${currentCoin}`);
                const data = await response.json();
                
                const container = document.getElementById('technical-analysis');
                
                if (data.indicators) {
                    let html = '';
                    Object.entries(data.indicators).forEach(([key, value]) => {
                        if (typeof value === 'object') {
                            html += `<h6>${key}</h6>`;
                            Object.entries(value).forEach(([subKey, subValue]) => {
                                html += `
                                    <div class="technical-indicator">
                                        <span>${subKey}</span>
                                        <span>${typeof subValue === 'number' ? subValue.toFixed(2) : subValue}</span>
                                    </div>
                                `;
                            });
                        }
                    });
                    container.innerHTML = html;
                } else {
                    container.innerHTML = '<p>لا توجد بيانات تحليل فني متاحة</p>';
                }
            } catch (error) {
                console.error('خطأ في تحديث التحليل الفني:', error);
            }
        }
        
        async function updatePredictions() {
            try {
                const response = await fetch(`/api/predictions/${currentCoin}`);
                const data = await response.json();
                
                const container = document.getElementById('predictions');
                
                if (data.predictions) {
                    let html = '';
                    Object.entries(data.predictions).forEach(([timeframe, prediction]) => {
                        const changeClass = prediction.price_change_percent >= 0 ? 'price-positive' : 'price-negative';
                        html += `
                            <div class="prediction-card">
                                <h6>${timeframe}</h6>
                                <p class="${changeClass}">
                                    ${prediction.direction} 
                                    (${prediction.price_change_percent.toFixed(2)}%)
                                </p>
                                <small>ثقة: ${(prediction.confidence * 100).toFixed(1)}%</small>
                            </div>
                        `;
                    });
                    container.innerHTML = html;
                } else {
                    container.innerHTML = '<p>لا توجد تنبؤات متاحة</p>';
                }
            } catch (error) {
                console.error('خطأ في تحديث التنبؤات:', error);
            }
        }
        
        function changeTimeframe(timeframe) {
            currentTimeframe = timeframe;
            
            // تحديث الأزرار النشطة
            document.querySelectorAll('.btn-group .btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            loadCoinChart();
        }
        
        function loadCoinChart() {
            currentCoin = document.getElementById('coin-selector').value;
            
            // عرض مؤشر التحميل
            document.getElementById('price-chart').innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    <p>جاري تحميل الشارت...</p>
                </div>
            `;
            
            // تحميل الشارت الجديد
            setTimeout(() => {
                fetch(`/api/generate-chart/${currentCoin}`)
                    .then(response => response.text())
                    .then(chartHtml => {
                        document.getElementById('price-chart').innerHTML = chartHtml;
                    })
                    .catch(error => {
                        console.error('خطأ في تحميل الشارت:', error);
                        document.getElementById('price-chart').innerHTML = 
                            '<p class="text-center">خطأ في تحميل الشارت</p>';
                    });
            }, 500);
            
            // تحديث التحليل الفني والتنبؤات للعملة الجديدة
            updateTechnicalAnalysis();
            updatePredictions();
        }
    </script>
</body>
</html>
        """
    
    def generate_interactive_chart(self, coin_id: str) -> str:
        """توليد شارت تفاعلي باستخدام Plotly"""
        try:
            # بيانات وهمية للشارت (في التطبيق الحقيقي ستأتي من API)
            import random
            from datetime import datetime, timedelta
            
            # توليد بيانات وهمية للأسعار
            dates = []
            prices = []
            volumes = []
            
            base_price = 45000 if coin_id == 'bitcoin' else 3000
            current_date = datetime.now() - timedelta(days=30)
            
            for i in range(30):
                dates.append(current_date + timedelta(days=i))
                # توليد سعر عشوائي مع اتجاه عام
                price_change = random.uniform(-0.05, 0.05)
                base_price *= (1 + price_change)
                prices.append(base_price)
                volumes.append(random.uniform(1000000, 5000000))
            
            # إنشاء الشارت
            fig = make_subplots(
                rows=2, cols=1,
                shared_xaxes=True,
                vertical_spacing=0.1,
                subplot_titles=('السعر', 'الحجم'),
                row_width=[0.7, 0.3]
            )
            
            # شارت السعر
            fig.add_trace(
                go.Scatter(
                    x=dates,
                    y=prices,
                    mode='lines',
                    name='السعر',
                    line=dict(color='#00ff88', width=2),
                    hovertemplate='<b>%{y:$,.2f}</b><br>%{x}<extra></extra>'
                ),
                row=1, col=1
            )
            
            # شارت الحجم
            fig.add_trace(
                go.Bar(
                    x=dates,
                    y=volumes,
                    name='الحجم',
                    marker_color='rgba(100, 149, 237, 0.7)',
                    hovertemplate='<b>%{y:$,.0f}</b><br>%{x}<extra></extra>'
                ),
                row=2, col=1
            )
            
            # تخصيص التصميم
            fig.update_layout(
                title=f'شارت {coin_id.title()}',
                template='plotly_dark',
                height=400,
                showlegend=False,
                margin=dict(l=0, r=0, t=30, b=0),
                paper_bgcolor='rgba(0,0,0,0)',
                plot_bgcolor='rgba(0,0,0,0)'
            )
            
            fig.update_xaxes(showgrid=True, gridwidth=1, gridcolor='rgba(255,255,255,0.1)')
            fig.update_yaxes(showgrid=True, gridwidth=1, gridcolor='rgba(255,255,255,0.1)')
            
            # تحويل إلى HTML
            chart_html = fig.to_html(
                include_plotlyjs=False,
                div_id="chart",
                config={'displayModeBar': False}
            )
            
            return chart_html
        
        except Exception as e:
            self.logger.error(f"خطأ في توليد الشارت: {e}")
            return '<p class="text-center">خطأ في توليد الشارت</p>'
    
    async def update_data_loop(self):
        """حلقة تحديث البيانات"""
        while self.running:
            try:
                await self.fetch_all_data()
                await asyncio.sleep(30)  # تحديث كل 30 ثانية
            except Exception as e:
                self.logger.error(f"خطأ في تحديث البيانات: {e}")
                await asyncio.sleep(60)  # انتظار دقيقة في حالة الخطأ
    
    async def fetch_all_data(self):
        """جلب جميع البيانات المطلوبة"""
        try:
            async with CryptoDataFetcher() as fetcher:
                # جلب بيانات السوق
                market_data = await fetcher.get_market_data(self.supported_coins)
                self.market_data['coins'] = list(market_data.values())
                
                # جلب البيانات التاريخية والتحليل الفني لكل عملة
                for coin_id in self.supported_coins[:3]:  # أول 3 عملات فقط لتوفير الوقت
                    try:
                        # البيانات التاريخية
                        historical_data = await fetcher.get_historical_data(coin_id, days=30)
                        self.chart_data[coin_id] = historical_data.to_dict('records') if not historical_data.empty else []
                        
                        # التحليل الفني
                        if coin_id in market_data:
                            analyzer = TechnicalAnalyzer()
                            technical_analysis = await analyzer.analyze(coin_id, market_data[coin_id])
                            self.technical_data[coin_id] = technical_analysis
                            
                            # التنبؤات
                            predictor = PredictionEngine()
                            predictions = await predictor.predict_price(coin_id, market_data[coin_id])
                            self.prediction_data[coin_id] = predictions
                    
                    except Exception as e:
                        self.logger.error(f"خطأ في معالجة بيانات {coin_id}: {e}")
                
                # مؤشر الخوف والطمع
                async with MCPIntegrations() as mcp:
                    fear_greed = await mcp.get_fear_greed_index()
                    self.market_data['fear_greed'] = fear_greed
        
        except Exception as e:
            self.logger.error(f"خطأ في جلب البيانات: {e}")
    
    def start_server(self):
        """بدء خادم لوحة التحكم"""
        self.running = True
        
        # بدء تحديث البيانات في خيط منفصل
        def run_update_loop():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self.update_data_loop())
        
        self.update_thread = threading.Thread(target=run_update_loop, daemon=True)
        self.update_thread.start()
        
        # بدء خادم Flask
        print(f"🌐 لوحة التحكم المتقدمة تعمل على: http://localhost:{self.port}")
        self.app.run(host='0.0.0.0', port=self.port, debug=False)
    
    def stop_server(self):
        """إيقاف خادم لوحة التحكم"""
        self.running = False

if __name__ == "__main__":
    dashboard = AdvancedCryptoDashboard()
    dashboard.start_server()