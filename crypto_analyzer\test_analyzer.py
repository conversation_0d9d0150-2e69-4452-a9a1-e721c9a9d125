#!/usr/bin/env python3
"""
ملف اختبار أداة تحليل العملات الرقمية
Test File for Cryptocurrency Analysis Tool
"""

import asyncio
import sys
import logging
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# استيراد الوحدات
from data_fetcher import CryptoDataFetcher
from technical_analyzer import TechnicalAnalyzer
from news_analyzer import NewsAnalyzer
from prediction_engine import PredictionEngine
from strategy_builder import StrategyBuilder
from portfolio_manager import PortfolioManager
from config import get_config, create_required_directories

# إعداد السجلات
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CryptoAnalyzerTester:
    """فئة اختبار أداة تحليل العملات الرقمية"""
    
    def __init__(self):
        self.test_coin = 'bitcoin'
        self.results = {}
        
    async def test_data_fetcher(self):
        """اختبار وحدة جلب البيانات"""
        print("🔍 اختبار وحدة جلب البيانات...")
        
        try:
            async with CryptoDataFetcher() as fetcher:
                # اختبار جلب بيانات السوق
                market_data = await fetcher.get_market_data([self.test_coin])
                
                if self.test_coin in market_data:
                    coin_data = market_data[self.test_coin]
                    print(f"✅ تم جلب بيانات {coin_data['name']}")
                    print(f"   💰 السعر الحالي: ${coin_data['current_price']:,.6f}")
                    print(f"   📊 القيمة السوقية: ${coin_data['market_cap']:,}")
                    print(f"   📈 التغيير 24 ساعة: {coin_data['price_change_percentage_24h']:.2f}%")
                    
                    self.results['data_fetcher'] = {
                        'status': 'نجح',
                        'data': coin_data
                    }
                else:
                    print(f"❌ فشل في جلب بيانات {self.test_coin}")
                    self.results['data_fetcher'] = {'status': 'فشل'}
                
                # اختبار البيانات التاريخية
                historical_data = await fetcher.get_historical_data(self.test_coin, days=30)
                
                if not historical_data.empty:
                    print(f"✅ تم جلب {len(historical_data)} نقطة بيانات تاريخية")
                    self.results['historical_data'] = {
                        'status': 'نجح',
                        'points': len(historical_data)
                    }
                else:
                    print("❌ فشل في جلب البيانات التاريخية")
                    self.results['historical_data'] = {'status': 'فشل'}
        
        except Exception as e:
            print(f"❌ خطأ في اختبار جلب البيانات: {e}")
            self.results['data_fetcher'] = {'status': 'خطأ', 'error': str(e)}
    
    async def test_technical_analyzer(self):
        """اختبار وحدة التحليل الفني"""
        print("\n📊 اختبار وحدة التحليل الفني...")
        
        try:
            if 'data_fetcher' not in self.results or self.results['data_fetcher']['status'] != 'نجح':
                print("⚠️  تخطي اختبار التحليل الفني - بيانات غير متوفرة")
                return
            
            market_data = self.results['data_fetcher']['data']
            analyzer = TechnicalAnalyzer()
            
            # تشغيل التحليل الفني
            analysis = await analyzer.analyze(self.test_coin, market_data)
            
            if analysis:
                print("✅ تم إجراء التحليل الفني بنجاح")
                
                # عرض بعض النتائج
                if 'indicators' in analysis:
                    indicators = analysis['indicators']
                    if 'rsi' in indicators:
                        rsi_value = indicators['rsi'].get('current', 'غير متوفر')
                        rsi_signal = indicators['rsi'].get('signal', 'غير متوفر')
                        print(f"   📈 RSI: {rsi_value:.2f} - {rsi_signal}")
                
                if 'recommendation' in analysis:
                    print(f"   🎯 التوصية: {analysis['recommendation']}")
                
                if 'strength_score' in analysis:
                    score = analysis['strength_score'].get('percentage', 0)
                    level = analysis['strength_score'].get('level', 'غير محدد')
                    print(f"   💪 قوة الإشارة: {score:.1f}% ({level})")
                
                self.results['technical_analysis'] = {
                    'status': 'نجح',
                    'recommendation': analysis.get('recommendation', 'غير محدد')
                }
            else:
                print("❌ فشل في التحليل الفني")
                self.results['technical_analysis'] = {'status': 'فشل'}
        
        except Exception as e:
            print(f"❌ خطأ في اختبار التحليل الفني: {e}")
            self.results['technical_analysis'] = {'status': 'خطأ', 'error': str(e)}
    
    async def test_news_analyzer(self):
        """اختبار وحدة تحليل الأخبار"""
        print("\n📰 اختبار وحدة تحليل الأخبار...")
        
        try:
            async with NewsAnalyzer() as analyzer:
                # جلب الأخبار
                news_items = await analyzer.fetch_crypto_news(hours_back=6)
                
                if news_items:
                    print(f"✅ تم جلب {len(news_items)} خبر")
                    
                    # عرض أول خبر
                    first_news = news_items[0]
                    print(f"   📰 أحدث خبر: {first_news.get('title', 'بدون عنوان')[:50]}...")
                    print(f"   📅 المصدر: {first_news.get('source', 'غير معروف')}")
                    
                    # تحليل المشاعر
                    sentiment_summary = await analyzer.get_market_sentiment_summary(hours_back=6)
                    
                    if sentiment_summary:
                        overall_sentiment = sentiment_summary.get('overall_sentiment', 'غير محدد')
                        confidence = sentiment_summary.get('confidence', 0)
                        print(f"   🎭 المشاعر العامة: {overall_sentiment} (ثقة: {confidence:.2f})")
                    
                    self.results['news_analysis'] = {
                        'status': 'نجح',
                        'news_count': len(news_items),
                        'sentiment': overall_sentiment
                    }
                else:
                    print("⚠️  لم يتم العثور على أخبار حديثة")
                    self.results['news_analysis'] = {'status': 'لا توجد أخبار'}
        
        except Exception as e:
            print(f"❌ خطأ في اختبار تحليل الأخبار: {e}")
            self.results['news_analysis'] = {'status': 'خطأ', 'error': str(e)}
    
    async def test_prediction_engine(self):
        """اختبار محرك التنبؤ"""
        print("\n🔮 اختبار محرك التنبؤ...")
        
        try:
            if 'data_fetcher' not in self.results or self.results['data_fetcher']['status'] != 'نجح':
                print("⚠️  تخطي اختبار التنبؤ - بيانات غير متوفرة")
                return
            
            market_data = self.results['data_fetcher']['data']
            predictor = PredictionEngine()
            
            # تشغيل التنبؤ
            prediction = await predictor.predict_price(self.test_coin, market_data)
            
            if prediction and 'predictions' in prediction:
                print("✅ تم إجراء التنبؤ بنجاح")
                
                predictions = prediction['predictions']
                if 'ensemble' in predictions:
                    ensemble = predictions['ensemble']
                    
                    # عرض تنبؤ 7 أيام
                    if '7_day' in ensemble:
                        pred_7d = ensemble['7_day']
                        direction = pred_7d.get('direction', 'غير محدد')
                        change_percent = pred_7d.get('price_change_percent', 0)
                        confidence = pred_7d.get('confidence', 0)
                        
                        print(f"   📅 توقع 7 أيام: {direction} ({change_percent:+.2f}%)")
                        print(f"   🎯 مستوى الثقة: {confidence:.2f}")
                
                # عرض التوصيات
                if 'recommendations' in prediction:
                    recommendations = prediction['recommendations']
                    short_term = recommendations.get('short_term', 'غير محدد')
                    print(f"   💡 توصية قصيرة المدى: {short_term}")
                
                self.results['prediction'] = {
                    'status': 'نجح',
                    'direction': direction,
                    'change_percent': change_percent
                }
            else:
                print("❌ فشل في التنبؤ")
                self.results['prediction'] = {'status': 'فشل'}
        
        except Exception as e:
            print(f"❌ خطأ في اختبار التنبؤ: {e}")
            self.results['prediction'] = {'status': 'خطأ', 'error': str(e)}
    
    async def test_strategy_builder(self):
        """اختبار بناء الاستراتيجيات"""
        print("\n⚡ اختبار بناء الاستراتيجيات...")
        
        try:
            if ('data_fetcher' not in self.results or 
                'technical_analysis' not in self.results or
                self.results['data_fetcher']['status'] != 'نجح'):
                print("⚠️  تخطي اختبار الاستراتيجيات - بيانات غير متوفرة")
                return
            
            strategy_builder = StrategyBuilder()
            
            # إعداد بيانات التحليل
            analysis_data = {
                'market_data': self.results['data_fetcher']['data'],
                'technical_analysis': self.results.get('technical_analysis', {}),
                'prediction': self.results.get('prediction', {})
            }
            
            # بناء الاستراتيجية
            strategy = await strategy_builder.build_strategy(self.test_coin, analysis_data)
            
            if strategy:
                print("✅ تم بناء الاستراتيجية بنجاح")
                
                strategy_type = strategy.get('strategy_name', 'غير محدد')
                print(f"   📊 نوع الاستراتيجية: {strategy_type}")
                
                evaluation = strategy.get('evaluation', {})
                if evaluation:
                    score = evaluation.get('score', 0)
                    rating = evaluation.get('overall_rating', 'غير محدد')
                    print(f"   🎯 تقييم الاستراتيجية: {score}/100 ({rating})")
                
                # عرض بعض قواعد التداول
                trading_rules = strategy.get('trading_rules', {})
                if 'entry_conditions' in trading_rules:
                    entry_conditions = trading_rules['entry_conditions'][:2]  # أول شرطين
                    print(f"   📈 شروط الدخول: {', '.join(entry_conditions)}")
                
                self.results['strategy'] = {
                    'status': 'نجح',
                    'type': strategy_type,
                    'score': score
                }
            else:
                print("❌ فشل في بناء الاستراتيجية")
                self.results['strategy'] = {'status': 'فشل'}
        
        except Exception as e:
            print(f"❌ خطأ في اختبار بناء الاستراتيجيات: {e}")
            self.results['strategy'] = {'status': 'خطأ', 'error': str(e)}
    
    async def test_portfolio_manager(self):
        """اختبار إدارة المحفظة"""
        print("\n💼 اختبار إدارة المحفظة...")
        
        try:
            portfolio_manager = PortfolioManager()
            
            # الحصول على ملخص المحفظة
            summary = portfolio_manager.get_portfolio_summary()
            
            if summary:
                print("✅ تم إنشاء مدير المحفظة بنجاح")
                print(f"   💰 إجمالي القيمة: ${summary['total_value']:,.2f}")
                print(f"   💵 الرصيد النقدي: ${summary['cash_balance']:,.2f}")
                print(f"   📍 عدد المراكز: {summary['positions_count']}")
                
                # اختبار تحديث استراتيجية وهمية
                if 'strategy' in self.results and self.results['strategy']['status'] == 'نجح':
                    test_strategy = {
                        'strategy_type': 'test',
                        'trading_rules': {'entry_conditions': ['test condition']},
                        'risk_management': {'max_position_size': 0.1},
                        'entry_exit_points': {'entry_points': [], 'exit_points': []},
                        'position_sizing': {'recommended_units': 0}
                    }
                    
                    await portfolio_manager.update_strategy(self.test_coin, test_strategy)
                    print("   ✅ تم اختبار تحديث الاستراتيجية")
                
                self.results['portfolio'] = {
                    'status': 'نجح',
                    'total_value': summary['total_value']
                }
            else:
                print("❌ فشل في إنشاء مدير المحفظة")
                self.results['portfolio'] = {'status': 'فشل'}
        
        except Exception as e:
            print(f"❌ خطأ في اختبار إدارة المحفظة: {e}")
            self.results['portfolio'] = {'status': 'خطأ', 'error': str(e)}
    
    def test_config(self):
        """اختبار ملف التكوين"""
        print("\n⚙️  اختبار ملف التكوين...")
        
        try:
            # اختبار الحصول على الإعدادات
            config = get_config()
            
            if config:
                print("✅ تم تحميل الإعدادات بنجاح")
                
                # اختبار إنشاء المجلدات
                create_required_directories()
                print("✅ تم إنشاء المجلدات المطلوبة")
                
                # عرض بعض الإعدادات
                watched_coins = config.get('watched_coins', [])
                print(f"   📋 العملات المراقبة: {len(watched_coins)} عملة")
                
                update_intervals = config.get('update_intervals', {})
                price_interval = update_intervals.get('price_monitoring', 0)
                print(f"   ⏱️  فترة مراقبة الأسعار: {price_interval} ثانية")
                
                self.results['config'] = {
                    'status': 'نجح',
                    'coins_count': len(watched_coins)
                }
            else:
                print("❌ فشل في تحميل الإعدادات")
                self.results['config'] = {'status': 'فشل'}
        
        except Exception as e:
            print(f"❌ خطأ في اختبار التكوين: {e}")
            self.results['config'] = {'status': 'خطأ', 'error': str(e)}
    
    def print_summary(self):
        """طباعة ملخص نتائج الاختبار"""
        print("\n" + "="*60)
        print("📋 ملخص نتائج الاختبار")
        print("="*60)
        
        total_tests = len(self.results)
        passed_tests = len([r for r in self.results.values() if r['status'] == 'نجح'])
        
        print(f"📊 إجمالي الاختبارات: {total_tests}")
        print(f"✅ الاختبارات الناجحة: {passed_tests}")
        print(f"❌ الاختبارات الفاشلة: {total_tests - passed_tests}")
        print(f"📈 معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
        
        print("\n📋 تفاصيل النتائج:")
        for test_name, result in self.results.items():
            status = result['status']
            status_icon = "✅" if status == 'نجح' else "❌" if status == 'فشل' else "⚠️"
            print(f"   {status_icon} {test_name}: {status}")
            
            if 'error' in result:
                print(f"      خطأ: {result['error']}")
        
        print("\n" + "="*60)
        
        if passed_tests == total_tests:
            print("🎉 تهانينا! جميع الاختبارات نجحت")
            print("🚀 أداة تحليل العملات الرقمية جاهزة للاستخدام")
        else:
            print("⚠️  بعض الاختبارات فشلت - يرجى مراجعة الأخطاء")
            print("💡 تأكد من اتصال الإنترنت وتثبيت جميع المتطلبات")
    
    async def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🧪 بدء اختبار أداة تحليل العملات الرقمية")
        print("="*60)
        
        # اختبار التكوين أولاً
        self.test_config()
        
        # اختبار الوحدات الأساسية
        await self.test_data_fetcher()
        await self.test_technical_analyzer()
        await self.test_news_analyzer()
        await self.test_prediction_engine()
        await self.test_strategy_builder()
        await self.test_portfolio_manager()
        
        # طباعة الملخص
        self.print_summary()

async def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 مرحباً بك في اختبار أداة تحليل العملات الرقمية")
    print("🔍 سيتم اختبار جميع الوحدات للتأكد من عملها بشكل صحيح")
    print("⏱️  قد يستغرق الاختبار بضع دقائق...")
    print()
    
    tester = CryptoAnalyzerTester()
    
    try:
        await tester.run_all_tests()
    except KeyboardInterrupt:
        print("\n⏹️  تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع في الاختبار: {e}")
        logger.exception("خطأ في الاختبار")

if __name__ == "__main__":
    asyncio.run(main())