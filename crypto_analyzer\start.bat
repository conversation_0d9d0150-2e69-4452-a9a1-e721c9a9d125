@echo off
chcp 65001 >nul
title أداة تحليل العملات الرقمية المتقدمة

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║        🚀 أداة تحليل العملات الرقمية المتقدمة 🚀           ║
echo ║                                                              ║
echo ║              Advanced Cryptocurrency Analysis Tool           ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت على النظام
    echo 💡 يرجى تثبيت Python 3.8 أو أحدث من: https://python.org
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
python --version

REM التحقق من وجود pip
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ pip غير متوفر
    echo 💡 يرجى إعادة تثبيت Python مع pip
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على pip

REM التحقق من وجود ملف المتطلبات
if not exist "requirements.txt" (
    echo ❌ ملف requirements.txt غير موجود
    echo 💡 تأكد من وجود جميع ملفات المشروع
    echo.
    pause
    exit /b 1
)

echo.
echo 🔍 فحص المتطلبات...

REM محاولة استيراد المكتبات الأساسية
python -c "import pandas, numpy, aiohttp" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  بعض المكتبات المطلوبة غير مثبتة
    echo 🔧 جاري تثبيت المتطلبات...
    echo.
    
    pip install -r requirements.txt
    
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت المتطلبات
        echo 💡 تحقق من اتصال الإنترنت وحاول مرة أخرى
        echo.
        pause
        exit /b 1
    )
    
    echo ✅ تم تثبيت المتطلبات بنجاح
) else (
    echo ✅ جميع المتطلبات مثبتة
)

echo.
echo 🚀 بدء تشغيل أداة تحليل العملات الرقمية...
echo.

REM تشغيل البرنامج
if exist "quick_start.py" (
    python quick_start.py
) else if exist "run.py" (
    python run.py
) else if exist "main.py" (
    python main.py
) else (
    echo ❌ لم يتم العثور على ملف التشغيل الرئيسي
    echo 💡 تأكد من وجود ملف quick_start.py أو run.py أو main.py
    echo.
    pause
    exit /b 1
)

REM في حالة انتهاء البرنامج
echo.
echo 👋 تم إنهاء البرنامج
echo 💡 يمكنك إغلاق هذه النافذة الآن
echo.
pause