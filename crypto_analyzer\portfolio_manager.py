"""
وحدة إدارة المحفظة الاستثمارية
Portfolio Management Module
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import json
import asyncio

class PortfolioManager:
    """فئة إدارة المحفظة الاستثمارية"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # إعدادات المحفظة الافتراضية
        self.portfolio_config = {
            'initial_balance': 10000,  # الرصيد الأولي بالدولار
            'max_positions': 10,       # أقصى عدد مراكز
            'rebalance_frequency': 7,  # إعادة التوازن كل 7 أيام
            'risk_tolerance': 'متوسط', # مستوى تحمل المخاطر
            'investment_horizon': 'متوسط المدى'  # الأفق الاستثماري
        }
        
        # المحفظة الحالية
        self.portfolio = {
            'cash_balance': self.portfolio_config['initial_balance'],
            'positions': {},  # {coin_id: position_info}
            'total_value': self.portfolio_config['initial_balance'],
            'unrealized_pnl': 0,
            'realized_pnl': 0,
            'created_at': datetime.now(),
            'last_updated': datetime.now()
        }
        
        # سجل المعاملات
        self.transaction_history = []
        
        # إعدادات إدارة المخاطر
        self.risk_management = {
            'max_position_size': 0.2,      # أقصى حجم مركز (20%)
            'max_sector_allocation': 0.4,   # أقصى تخصيص لقطاع (40%)
            'stop_loss_threshold': 0.15,    # حد وقف الخسارة (15%)
            'take_profit_threshold': 0.3,   # حد جني الأرباح (30%)
            'correlation_limit': 0.7,       # حد الارتباط بين الأصول
            'volatility_limit': 0.5         # حد التقلبات
        }
        
        # مؤشرات الأداء
        self.performance_metrics = {
            'total_return': 0,
            'annualized_return': 0,
            'sharpe_ratio': 0,
            'max_drawdown': 0,
            'win_rate': 0,
            'profit_factor': 0,
            'volatility': 0
        }
    
    async def update_strategy(self, coin_id: str, strategy: Dict):
        """تحديث استراتيجية العملة في المحفظة"""
        try:
            # تحديث أو إضافة الاستراتيجية
            if coin_id not in self.portfolio['positions']:
                self.portfolio['positions'][coin_id] = {
                    'quantity': 0,
                    'average_price': 0,
                    'current_price': 0,
                    'market_value': 0,
                    'unrealized_pnl': 0,
                    'strategy': strategy,
                    'last_updated': datetime.now()
                }
            else:
                self.portfolio['positions'][coin_id]['strategy'] = strategy
                self.portfolio['positions'][coin_id]['last_updated'] = datetime.now()
            
            # تنفيذ إشارات التداول
            await self._execute_strategy_signals(coin_id, strategy)
            
            # تحديث قيم المحفظة
            await self._update_portfolio_values()
            
            self.logger.info(f"تم تحديث استراتيجية {coin_id}")
        
        except Exception as e:
            self.logger.error(f"خطأ في تحديث استراتيجية {coin_id}: {e}")
    
    async def _execute_strategy_signals(self, coin_id: str, strategy: Dict):
        """تنفيذ إشارات التداول من الاستراتيجية"""
        try:
            # الحصول على الإشارة الحالية
            trading_rules = strategy.get('trading_rules', {})
            entry_exit_points = strategy.get('entry_exit_points', {})
            position_sizing = strategy.get('position_sizing', {})
            
            # الحصول على السعر الحالي
            current_price = await self._get_current_price(coin_id)
            
            if current_price == 0:
                return
            
            # تحديث السعر الحالي في المركز
            if coin_id in self.portfolio['positions']:
                self.portfolio['positions'][coin_id]['current_price'] = current_price
            
            # تحليل إشارات الدخول
            await self._analyze_entry_signals(coin_id, strategy, current_price)
            
            # تحليل إشارات الخروج
            await self._analyze_exit_signals(coin_id, strategy, current_price)
            
            # تطبيق إدارة المخاطر
            await self._apply_risk_management(coin_id, current_price)
        
        except Exception as e:
            self.logger.error(f"خطأ في تنفيذ إشارات {coin_id}: {e}")
    
    async def _analyze_entry_signals(self, coin_id: str, strategy: Dict, current_price: float):
        """تحليل إشارات الدخول"""
        try:
            position = self.portfolio['positions'].get(coin_id, {})
            current_quantity = position.get('quantity', 0)
            
            # إذا كان لدينا مركز كامل، لا نحتاج دخول جديد
            position_sizing = strategy.get('position_sizing', {})
            max_units = position_sizing.get('recommended_units', 0)
            
            if current_quantity >= max_units:
                return
            
            # تحليل نقاط الدخول
            entry_points = strategy.get('entry_exit_points', {}).get('entry_points', [])
            
            for entry_point in entry_points:
                entry_price = entry_point.get('price', 0)
                confidence = entry_point.get('confidence', 'متوسط')
                
                # إذا كان السعر الحالي قريب من نقطة الدخول
                if abs(current_price - entry_price) / entry_price <= 0.02:  # ضمن 2%
                    # تحديد حجم الشراء
                    buy_quantity = await self._calculate_buy_quantity(
                        coin_id, strategy, current_price, confidence
                    )
                    
                    if buy_quantity > 0:
                        await self._execute_buy_order(coin_id, buy_quantity, current_price, entry_point)
        
        except Exception as e:
            self.logger.error(f"خطأ في تحليل إشارات الدخول لـ {coin_id}: {e}")
    
    async def _analyze_exit_signals(self, coin_id: str, strategy: Dict, current_price: float):
        """تحليل إشارات الخروج"""
        try:
            position = self.portfolio['positions'].get(coin_id, {})
            current_quantity = position.get('quantity', 0)
            
            if current_quantity <= 0:
                return
            
            # تحليل نقاط الخروج
            exit_points = strategy.get('entry_exit_points', {}).get('exit_points', [])
            
            for exit_point in exit_points:
                exit_price = exit_point.get('price', 0)
                confidence = exit_point.get('confidence', 'متوسط')
                
                # إذا كان السعر الحالي قريب من نقطة الخروج
                if abs(current_price - exit_price) / exit_price <= 0.02:  # ضمن 2%
                    # تحديد حجم البيع
                    sell_quantity = await self._calculate_sell_quantity(
                        coin_id, strategy, current_price, confidence
                    )
                    
                    if sell_quantity > 0:
                        await self._execute_sell_order(coin_id, sell_quantity, current_price, exit_point)
        
        except Exception as e:
            self.logger.error(f"خطأ في تحليل إشارات الخروج لـ {coin_id}: {e}")
    
    async def _apply_risk_management(self, coin_id: str, current_price: float):
        """تطبيق إدارة المخاطر"""
        try:
            position = self.portfolio['positions'].get(coin_id, {})
            
            if not position or position.get('quantity', 0) <= 0:
                return
            
            average_price = position.get('average_price', 0)
            quantity = position.get('quantity', 0)
            
            if average_price == 0:
                return
            
            # حساب الربح/الخسارة النسبية
            pnl_percent = (current_price - average_price) / average_price
            
            # تطبيق وقف الخسارة
            if pnl_percent <= -self.risk_management['stop_loss_threshold']:
                await self._execute_stop_loss(coin_id, quantity, current_price)
                return
            
            # تطبيق جني الأرباح
            if pnl_percent >= self.risk_management['take_profit_threshold']:
                # بيع جزئي (50% من المركز)
                sell_quantity = quantity * 0.5
                await self._execute_take_profit(coin_id, sell_quantity, current_price)
                return
            
            # فحص حجم المركز
            position_value = quantity * current_price
            portfolio_value = self.portfolio['total_value']
            
            if portfolio_value > 0:
                position_percent = position_value / portfolio_value
                
                # إذا تجاوز المركز الحد الأقصى
                if position_percent > self.risk_management['max_position_size']:
                    # تقليل حجم المركز
                    target_value = portfolio_value * self.risk_management['max_position_size']
                    target_quantity = target_value / current_price
                    excess_quantity = quantity - target_quantity
                    
                    if excess_quantity > 0:
                        await self._execute_rebalance_sell(coin_id, excess_quantity, current_price)
        
        except Exception as e:
            self.logger.error(f"خطأ في تطبيق إدارة المخاطر لـ {coin_id}: {e}")
    
    async def _calculate_buy_quantity(self, coin_id: str, strategy: Dict, 
                                    current_price: float, confidence: str) -> float:
        """حساب كمية الشراء"""
        try:
            position_sizing = strategy.get('position_sizing', {})
            recommended_units = position_sizing.get('recommended_units', 0)
            
            # تعديل الكمية حسب مستوى الثقة
            confidence_multiplier = {
                'عالي': 1.0,
                'متوسط': 0.7,
                'منخفض': 0.4
            }.get(confidence, 0.7)
            
            # تعديل الكمية حسب الرصيد المتاح
            available_cash = self.portfolio['cash_balance']
            max_affordable = available_cash / current_price
            
            # الكمية المقترحة
            target_quantity = recommended_units * confidence_multiplier
            
            # الكمية الفعلية (الأقل من المتاح والمقترح)
            buy_quantity = min(target_quantity, max_affordable)
            
            # التأكد من وجود رصيد كافي
            required_cash = buy_quantity * current_price
            if required_cash > available_cash:
                buy_quantity = available_cash / current_price
            
            return max(0, buy_quantity)
        
        except Exception as e:
            self.logger.error(f"خطأ في حساب كمية الشراء: {e}")
            return 0
    
    async def _calculate_sell_quantity(self, coin_id: str, strategy: Dict,
                                     current_price: float, confidence: str) -> float:
        """حساب كمية البيع"""
        try:
            position = self.portfolio['positions'].get(coin_id, {})
            current_quantity = position.get('quantity', 0)
            
            if current_quantity <= 0:
                return 0
            
            # تعديل الكمية حسب مستوى الثقة
            confidence_multiplier = {
                'عالي': 1.0,      # بيع كامل
                'متوسط': 0.5,    # بيع جزئي
                'منخفض': 0.25    # بيع قليل
            }.get(confidence, 0.5)
            
            sell_quantity = current_quantity * confidence_multiplier
            
            return max(0, sell_quantity)
        
        except Exception as e:
            self.logger.error(f"خطأ في حساب كمية البيع: {e}")
            return 0
    
    async def _execute_buy_order(self, coin_id: str, quantity: float, 
                               price: float, entry_point: Dict):
        """تنفيذ أمر شراء"""
        try:
            total_cost = quantity * price
            
            # التحقق من توفر الرصيد
            if total_cost > self.portfolio['cash_balance']:
                self.logger.warning(f"رصيد غير كافي لشراء {coin_id}")
                return
            
            # تحديث المركز
            if coin_id not in self.portfolio['positions']:
                self.portfolio['positions'][coin_id] = {
                    'quantity': 0,
                    'average_price': 0,
                    'current_price': price,
                    'market_value': 0,
                    'unrealized_pnl': 0
                }
            
            position = self.portfolio['positions'][coin_id]
            old_quantity = position['quantity']
            old_average_price = position['average_price']
            
            # حساب السعر المتوسط الجديد
            if old_quantity > 0:
                total_quantity = old_quantity + quantity
                total_cost_basis = (old_quantity * old_average_price) + (quantity * price)
                new_average_price = total_cost_basis / total_quantity
            else:
                total_quantity = quantity
                new_average_price = price
            
            # تحديث المركز
            position['quantity'] = total_quantity
            position['average_price'] = new_average_price
            position['current_price'] = price
            position['market_value'] = total_quantity * price
            position['unrealized_pnl'] = (price - new_average_price) * total_quantity
            
            # تحديث الرصيد النقدي
            self.portfolio['cash_balance'] -= total_cost
            
            # تسجيل المعاملة
            transaction = {
                'type': 'buy',
                'coin_id': coin_id,
                'quantity': quantity,
                'price': price,
                'total_value': total_cost,
                'timestamp': datetime.now(),
                'reason': f"دخول عند {entry_point.get('type', 'نقطة دخول')}",
                'confidence': entry_point.get('confidence', 'متوسط')
            }
            
            self.transaction_history.append(transaction)
            
            self.logger.info(f"تم شراء {quantity:.6f} من {coin_id} بسعر {price:.6f}")
        
        except Exception as e:
            self.logger.error(f"خطأ في تنفيذ أمر شراء {coin_id}: {e}")
    
    async def _execute_sell_order(self, coin_id: str, quantity: float,
                                price: float, exit_point: Dict):
        """تنفيذ أمر بيع"""
        try:
            position = self.portfolio['positions'].get(coin_id, {})
            current_quantity = position.get('quantity', 0)
            
            if quantity > current_quantity:
                quantity = current_quantity
            
            if quantity <= 0:
                return
            
            total_value = quantity * price
            average_price = position.get('average_price', 0)
            
            # حساب الربح/الخسارة المحققة
            realized_pnl = (price - average_price) * quantity
            
            # تحديث المركز
            remaining_quantity = current_quantity - quantity
            position['quantity'] = remaining_quantity
            
            if remaining_quantity > 0:
                position['market_value'] = remaining_quantity * price
                position['unrealized_pnl'] = (price - average_price) * remaining_quantity
            else:
                # إغلاق المركز بالكامل
                position['market_value'] = 0
                position['unrealized_pnl'] = 0
            
            position['current_price'] = price
            
            # تحديث الرصيد النقدي والأرباح المحققة
            self.portfolio['cash_balance'] += total_value
            self.portfolio['realized_pnl'] += realized_pnl
            
            # تسجيل المعاملة
            transaction = {
                'type': 'sell',
                'coin_id': coin_id,
                'quantity': quantity,
                'price': price,
                'total_value': total_value,
                'realized_pnl': realized_pnl,
                'timestamp': datetime.now(),
                'reason': f"خروج عند {exit_point.get('type', 'نقطة خروج')}",
                'confidence': exit_point.get('confidence', 'متوسط')
            }
            
            self.transaction_history.append(transaction)
            
            self.logger.info(f"تم بيع {quantity:.6f} من {coin_id} بسعر {price:.6f} (ربح/خسارة: {realized_pnl:.2f})")
        
        except Exception as e:
            self.logger.error(f"خطأ في تنفيذ أمر بيع {coin_id}: {e}")
    
    async def _execute_stop_loss(self, coin_id: str, quantity: float, price: float):
        """تنفيذ وقف الخسارة"""
        try:
            exit_point = {
                'type': 'وقف خسارة',
                'confidence': 'عالي'
            }
            
            await self._execute_sell_order(coin_id, quantity, price, exit_point)
            
            self.logger.warning(f"تم تنفيذ وقف الخسارة لـ {coin_id} عند {price:.6f}")
        
        except Exception as e:
            self.logger.error(f"خطأ في تنفيذ وقف الخسارة لـ {coin_id}: {e}")
    
    async def _execute_take_profit(self, coin_id: str, quantity: float, price: float):
        """تنفيذ جني الأرباح"""
        try:
            exit_point = {
                'type': 'جني أرباح',
                'confidence': 'عالي'
            }
            
            await self._execute_sell_order(coin_id, quantity, price, exit_point)
            
            self.logger.info(f"تم جني الأرباح لـ {coin_id} عند {price:.6f}")
        
        except Exception as e:
            self.logger.error(f"خطأ في جني الأرباح لـ {coin_id}: {e}")
    
    async def _execute_rebalance_sell(self, coin_id: str, quantity: float, price: float):
        """تنفيذ بيع إعادة التوازن"""
        try:
            exit_point = {
                'type': 'إعادة توازن',
                'confidence': 'متوسط'
            }
            
            await self._execute_sell_order(coin_id, quantity, price, exit_point)
            
            self.logger.info(f"تم بيع إعادة التوازن لـ {coin_id} عند {price:.6f}")
        
        except Exception as e:
            self.logger.error(f"خطأ في بيع إعادة التوازن لـ {coin_id}: {e}")
    
    async def _get_current_price(self, coin_id: str) -> float:
        """الحصول على السعر الحالي للعملة"""
        try:
            from data_fetcher import CryptoDataFetcher
            
            async with CryptoDataFetcher() as fetcher:
                market_data = await fetcher.get_market_data([coin_id])
                
                if coin_id in market_data:
                    return market_data[coin_id].get('current_price', 0)
            
            return 0
        
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على سعر {coin_id}: {e}")
            return 0
    
    async def _update_portfolio_values(self):
        """تحديث قيم المحفظة"""
        try:
            total_market_value = 0
            total_unrealized_pnl = 0
            
            for coin_id, position in self.portfolio['positions'].items():
                if position['quantity'] > 0:
                    # تحديث السعر الحالي
                    current_price = await self._get_current_price(coin_id)
                    
                    if current_price > 0:
                        position['current_price'] = current_price
                        position['market_value'] = position['quantity'] * current_price
                        position['unrealized_pnl'] = (current_price - position['average_price']) * position['quantity']
                        
                        total_market_value += position['market_value']
                        total_unrealized_pnl += position['unrealized_pnl']
            
            # تحديث إجمالي قيمة المحفظة
            self.portfolio['total_value'] = self.portfolio['cash_balance'] + total_market_value
            self.portfolio['unrealized_pnl'] = total_unrealized_pnl
            self.portfolio['last_updated'] = datetime.now()
            
            # حساب مؤشرات الأداء
            await self._calculate_performance_metrics()
        
        except Exception as e:
            self.logger.error(f"خطأ في تحديث قيم المحفظة: {e}")
    
    async def _calculate_performance_metrics(self):
        """حساب مؤشرات الأداء"""
        try:
            initial_balance = self.portfolio_config['initial_balance']
            current_value = self.portfolio['total_value']
            
            # العائد الإجمالي
            total_return = (current_value - initial_balance) / initial_balance
            self.performance_metrics['total_return'] = total_return
            
            # حساب العائد السنوي (تقريبي)
            days_elapsed = (datetime.now() - self.portfolio['created_at']).days
            if days_elapsed > 0:
                annualized_return = ((current_value / initial_balance) ** (365 / days_elapsed)) - 1
                self.performance_metrics['annualized_return'] = annualized_return
            
            # حساب معدل النجاح
            if self.transaction_history:
                profitable_trades = sum(1 for t in self.transaction_history 
                                      if t.get('realized_pnl', 0) > 0)
                total_trades = len([t for t in self.transaction_history if t['type'] == 'sell'])
                
                if total_trades > 0:
                    self.performance_metrics['win_rate'] = profitable_trades / total_trades
            
            # حساب نسبة الربح
            total_profits = sum(t.get('realized_pnl', 0) for t in self.transaction_history 
                              if t.get('realized_pnl', 0) > 0)
            total_losses = abs(sum(t.get('realized_pnl', 0) for t in self.transaction_history 
                                 if t.get('realized_pnl', 0) < 0))
            
            if total_losses > 0:
                self.performance_metrics['profit_factor'] = total_profits / total_losses
            
        except Exception as e:
            self.logger.error(f"خطأ في حساب مؤشرات الأداء: {e}")
    
    async def rebalance_portfolio(self):
        """إعادة توازن المحفظة"""
        try:
            await self._update_portfolio_values()
            
            total_value = self.portfolio['total_value']
            target_allocations = await self._calculate_target_allocations()
            
            for coin_id, target_percent in target_allocations.items():
                current_position = self.portfolio['positions'].get(coin_id, {})
                current_value = current_position.get('market_value', 0)
                current_percent = current_value / total_value if total_value > 0 else 0
                
                # إذا كان الانحراف أكبر من 5%
                if abs(current_percent - target_percent) > 0.05:
                    target_value = total_value * target_percent
                    difference = target_value - current_value
                    
                    current_price = await self._get_current_price(coin_id)
                    
                    if current_price > 0:
                        if difference > 0:
                            # نحتاج للشراء
                            buy_quantity = difference / current_price
                            if buy_quantity * current_price <= self.portfolio['cash_balance']:
                                entry_point = {'type': 'إعادة توازن', 'confidence': 'متوسط'}
                                await self._execute_buy_order(coin_id, buy_quantity, current_price, entry_point)
                        else:
                            # نحتاج للبيع
                            sell_quantity = abs(difference) / current_price
                            await self._execute_rebalance_sell(coin_id, sell_quantity, current_price)
            
            self.logger.info("تم إعادة توازن المحفظة")
        
        except Exception as e:
            self.logger.error(f"خطأ في إعادة توازن المحفظة: {e}")
    
    async def _calculate_target_allocations(self) -> Dict[str, float]:
        """حساب التخصيصات المستهدفة"""
        try:
            # تخصيص متساوي بسيط (يمكن تطويره لاحقاً)
            active_positions = [coin_id for coin_id, pos in self.portfolio['positions'].items() 
                              if pos.get('quantity', 0) > 0]
            
            if not active_positions:
                return {}
            
            equal_allocation = 1.0 / len(active_positions)
            
            return {coin_id: equal_allocation for coin_id in active_positions}
        
        except Exception as e:
            self.logger.error(f"خطأ في حساب التخصيصات المستهدفة: {e}")
            return {}
    
    def get_portfolio_summary(self) -> Dict:
        """الحصول على ملخص المحفظة"""
        try:
            summary = {
                'total_value': self.portfolio['total_value'],
                'cash_balance': self.portfolio['cash_balance'],
                'invested_amount': self.portfolio['total_value'] - self.portfolio['cash_balance'],
                'unrealized_pnl': self.portfolio['unrealized_pnl'],
                'realized_pnl': self.portfolio['realized_pnl'],
                'total_pnl': self.portfolio['unrealized_pnl'] + self.portfolio['realized_pnl'],
                'performance_metrics': self.performance_metrics,
                'positions_count': len([p for p in self.portfolio['positions'].values() 
                                      if p.get('quantity', 0) > 0]),
                'last_updated': self.portfolio['last_updated']
            }
            
            # إضافة تفاصيل المراكز
            summary['positions'] = {}
            for coin_id, position in self.portfolio['positions'].items():
                if position.get('quantity', 0) > 0:
                    summary['positions'][coin_id] = {
                        'quantity': position['quantity'],
                        'average_price': position['average_price'],
                        'current_price': position['current_price'],
                        'market_value': position['market_value'],
                        'unrealized_pnl': position['unrealized_pnl'],
                        'pnl_percent': (position['unrealized_pnl'] / (position['quantity'] * position['average_price'])) * 100 if position['average_price'] > 0 else 0,
                        'allocation_percent': (position['market_value'] / self.portfolio['total_value']) * 100 if self.portfolio['total_value'] > 0 else 0
                    }
            
            return summary
        
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء ملخص المحفظة: {e}")
            return {}
    
    def get_transaction_history(self, limit: int = 50) -> List[Dict]:
        """الحصول على سجل المعاملات"""
        try:
            # ترتيب المعاملات حسب التاريخ (الأحدث أولاً)
            sorted_transactions = sorted(
                self.transaction_history,
                key=lambda x: x['timestamp'],
                reverse=True
            )
            
            return sorted_transactions[:limit]
        
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على سجل المعاملات: {e}")
            return []
    
    def save_portfolio(self, filename: str = None):
        """حفظ المحفظة"""
        try:
            if not filename:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"portfolio_{timestamp}.json"
            
            filepath = f"data/portfolio/{filename}"
            
            portfolio_data = {
                'portfolio': self.portfolio,
                'transaction_history': self.transaction_history,
                'performance_metrics': self.performance_metrics,
                'config': self.portfolio_config,
                'risk_management': self.risk_management
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(portfolio_data, f, ensure_ascii=False, indent=2, default=str)
            
            self.logger.info(f"تم حفظ المحفظة في {filepath}")
        
        except Exception as e:
            self.logger.error(f"خطأ في حفظ المحفظة: {e}")
    
    def load_portfolio(self, filepath: str):
        """تحميل محفظة محفوظة"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.portfolio = data.get('portfolio', self.portfolio)
            self.transaction_history = data.get('transaction_history', [])
            self.performance_metrics = data.get('performance_metrics', self.performance_metrics)
            self.portfolio_config = data.get('config', self.portfolio_config)
            self.risk_management = data.get('risk_management', self.risk_management)
            
            # تحويل التواريخ من string إلى datetime
            for transaction in self.transaction_history:
                if isinstance(transaction['timestamp'], str):
                    transaction['timestamp'] = datetime.fromisoformat(transaction['timestamp'])
            
            self.logger.info(f"تم تحميل المحفظة من {filepath}")
        
        except Exception as e:
            self.logger.error(f"خطأ في تحميل المحفظة: {e}")