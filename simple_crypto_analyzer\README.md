# 🚀 محلل العملات الرقمية المبسط
## Simple Cryptocurrency Analyzer v2.0

أداة تحليل العملات الرقمية مبسطة وفعالة باللغة العربية

---

## ✨ الميزات

### 📊 البيانات الحقيقية
- أسعار مباشرة من CoinGecko API
- 10 عملات رقمية رئيسية
- بيانات تاريخية وحجم التداول
- قاعدة بيانات محلية للتخزين المؤقت

### 📈 التحليل الفني
- مؤشر RSI (قوة الاتجاه النسبية)
- المتوسطات المتحركة (20 و 50)
- Bollinger Bands
- توصيات تداول ذكية

### 🎨 واجهة مستخدم حديثة
- تصميم عربي جميل
- شارتات تفاعلية مع Plotly
- تحديث مباشر للبيانات
- سهولة في الاستخدام

### 💾 قاعدة بيانات محلية
- حفظ البيانات في SQLite
- عمل بدون إنترنت (البيانات المحفوظة)
- سرعة في التحميل

---

## 🚀 التشغيل السريع

### الطريقة الأسهل (ضغطتين):
1. اضغط ضغطتين على `run.bat`
2. انتظر فتح المتصفح تلقائياً

### الطريقة اليدوية:
```bash
# تثبيت المتطلبات
pip install -r requirements.txt

# تشغيل التطبيق
streamlit run app.py
```

---

## 📋 المتطلبات

### النظام:
- Windows 10/11
- Python 3.8 أو أحدث
- اتصال إنترنت (للبيانات المباشرة)

### المكتبات:
- streamlit (واجهة المستخدم)
- pandas (معالجة البيانات)
- plotly (الشارتات)
- requests (جلب البيانات)
- sqlite3 (قاعدة البيانات - مدمجة)

---

## 🎯 العملات المدعومة

1. **Bitcoin (BTC)** - البيتكوين
2. **Ethereum (ETH)** - الإيثيريوم
3. **Binance Coin (BNB)** - عملة بينانس
4. **Cardano (ADA)** - كاردانو
5. **Solana (SOL)** - سولانا
6. **Polkadot (DOT)** - بولكادوت
7. **Dogecoin (DOGE)** - دوجكوين
8. **Avalanche (AVAX)** - أفالانش
9. **Chainlink (LINK)** - تشين لينك
10. **Polygon (MATIC)** - بوليجون

---

## 📊 المؤشرات الفنية

### RSI (مؤشر القوة النسبية)
- **أقل من 30**: ذروة بيع (فرصة شراء)
- **أكثر من 70**: ذروة شراء (فرصة بيع)
- **30-70**: منطقة محايدة

### المتوسطات المتحركة
- **SMA 20**: متوسط 20 فترة
- **SMA 50**: متوسط 50 فترة
- **الإشارة**: السعر فوق/تحت المتوسط

### Bollinger Bands
- **الحد العلوي**: مقاومة محتملة
- **الحد السفلي**: دعم محتمل
- **الوسط**: المتوسط المتحرك

---

## 🎨 كيفية الاستخدام

### 1. اختيار العملة
- استخدم القائمة المنسدلة في الشريط الجانبي
- اختر من 10 عملات رقمية رئيسية

### 2. تحديد الفترة الزمنية
- 7 أيام (للتحليل قصير المدى)
- 30 يوم (للتحليل متوسط المدى)
- 90 يوم (للتحليل طويل المدى)

### 3. قراءة التحليل
- **شارت الأسعار**: يظهر حركة السعر والحجم
- **المؤشرات الفنية**: RSI والمتوسطات
- **التوصية**: شراء/بيع/انتظار

### 4. مراقبة جميع العملات
- جدول شامل بجميع العملات
- الأسعار الحالية والتغييرات
- القيم السوقية والأحجام

---

## ⚠️ تحذيرات مهمة

### 🚨 للأغراض التعليمية فقط
- هذه الأداة مخصصة للتعلم والتدريب
- **لا تستخدم أموال حقيقية** بناءً على هذه التوصيات
- **استشر خبير مالي** قبل اتخاذ قرارات استثمارية

### 📊 دقة البيانات
- البيانات من مصادر موثوقة (CoinGecko)
- قد تحدث تأخيرات في التحديث
- التحليل الفني ليس ضماناً للنتائج

### 🔒 الأمان
- لا نطلب معلومات شخصية
- لا نحفظ بيانات حساسة
- جميع البيانات محلية

---

## 🔧 استكشاف الأخطاء

### مشكلة: لا يعمل التطبيق
**الحل:**
1. تأكد من تثبيت Python
2. ثبت المتطلبات: `pip install -r requirements.txt`
3. شغل: `streamlit run app.py`

### مشكلة: لا تظهر البيانات
**الحل:**
1. تحقق من اتصال الإنترنت
2. انتظر قليلاً (قد يكون هناك تأخير)
3. اضغط "تحديث البيانات"

### مشكلة: الشارتات لا تظهر
**الحل:**
1. تأكد من تثبيت plotly: `pip install plotly`
2. حدث المتصفح
3. امسح cache المتصفح

---

## 📈 مقارنة مع النسخة المعقدة

| الميزة | النسخة المبسطة | النسخة المعقدة |
|--------|-----------------|------------------|
| سهولة التثبيت | ✅ سهل جداً | ❌ معقد |
| سرعة التشغيل | ✅ سريع | ❌ بطيء |
| استقرار العمل | ✅ مستقر | ❌ أخطاء كثيرة |
| البيانات الحقيقية | ✅ حقيقية | ❌ معظمها وهمي |
| واجهة المستخدم | ✅ جميلة وبسيطة | ❌ معقدة |
| المتطلبات | ✅ قليلة | ❌ كثيرة ومعقدة |

---

## 🎯 الخلاصة

هذه النسخة المبسطة تركز على:
- **الفعالية** بدلاً من التعقيد
- **البيانات الحقيقية** بدلاً من الوهمية
- **سهولة الاستخدام** بدلاً من الميزات الكثيرة
- **الاستقرار** بدلاً من الأخطاء

---

## 📞 الدعم

إذا واجهت أي مشاكل:
1. راجع قسم استكشاف الأخطاء
2. تأكد من تثبيت جميع المتطلبات
3. تحقق من اتصال الإنترنت

---

**تطوير: فريق التطوير**  
**الإصدار: 2.0**  
**التاريخ: 2024**

🚀 **استمتع بتحليل العملات الرقمية بطريقة بسيطة وفعالة!**